import React, { useState, useEffect, useContext } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Image,
  StyleSheet,
  Modal,
  ScrollView,
  StatusBar,
} from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
import globalStyle from "../styles/global";
import GenerateQRCode from "./GenerateQRCode";
import Card from "../components/Card";
import PremiumCard from "../components/PremiumCard";
import { AuthContext } from "../services/AuthContext";
import AdBanner from "../components/AdBanner";

const HomeCard = ({ name, text, onPress, color, theme }) => (
  <TouchableOpacity 
    onPress={onPress} 
    style={[styles.card, { backgroundColor: theme?.cardColor || color }]}
  >
    <View style={styles.cardContent}>
      <Icon name={name} size={36} color={theme?.textColor || "#FFF"} />
      <Text style={[styles.cardText, { color: theme?.textColor || "#FFF" }]}>
        {text}
      </Text>
    </View>
  </TouchableOpacity>
);
const Home = ({ navigation }) => {
  const [modalVisible, setModalVisible] = useState(false);
  const [greeting, setGreeting] = useState("");
  const { logout, user, setUser, theme, toggleTheme } = useContext(AuthContext);

  const mainPage = globalStyle(theme);

  useEffect(() => {
    const currentHour = new Date().getHours();
    if (currentHour < 12) {
      setGreeting("Good Morning ☀️");
    } else if (currentHour < 18) {
      setGreeting("Good Afternoon 🌤️");
    } else {
      setGreeting("Good Evening 🌙");
    }
  }, []);

  return (
    <View style={mainPage.container}>
      <StatusBar backgroundColor={theme.primaryColor} barStyle={theme.isLight ? "dark-content" : "light-content"} />
      
      <View style={styles.welcomeContainer}>
        <Text style={[styles.greetingText, { color: theme.textColor }]}>
          {greeting}
        </Text>
        <Text style={[styles.welcomeText, { color: theme.textColor }]}>
          Welcome to QR Parking
        </Text>
      </View>

      {/* Add Banner Ad here */}
      <AdBanner />

      <ScrollView showsVerticalScrollIndicator={false}>
        <View style={styles.qrContainer}>
          <Image
            source={require("../Assets/qrLogo.png")}
            style={styles.qrImage}
          />
        </View>

        <View style={styles.featureContainer}>
          <HomeCard
            name="qrcode"
            text="Generate QR Code"
            onPress={() => setModalVisible(true)}
            color="#4CAF50"
            theme={theme}
          />
          <HomeCard
            name="camera"
            text="Scan QR Code"
            onPress={() => navigation.navigate("ScanQRCode")}
            color="#2A5C8A"
            theme={theme}
          />
          <HomeCard
            name="history"
            text="Call Logs"
            onPress={() => navigation.navigate("CallLogs")}
            color="#FF9800"
            theme={theme}
          />
          <HomeCard
            name="car-clock"
            text="Parking Timer"
            onPress={() => navigation.navigate("ParkingTimer")}
            color="#9C27B0"
            theme={theme}
          />
        </View>
        
        <View style={styles.parkingTimerContainer}>
          <TouchableOpacity 
            style={styles.parkingTimerCard}
            onPress={() => navigation.navigate("ParkingTimer")}
          >
            <View style={styles.parkingTimerContent}>
              <Icon name="car-clock" size={40} color="#FFF" />
              <View style={styles.parkingTimerTextContainer}>
                <Text style={styles.parkingTimerTitle}>Parking Timer</Text>
                <Text style={styles.parkingTimerSubtitle}>Track your parking time and costs</Text>
              </View>
              <Icon name="chevron-right" size={30} color="#FFF" />
            </View>
          </TouchableOpacity>
        </View>

        <View style={styles.premiumWrapper}>
          <PremiumCard />
        </View>
      </ScrollView>

      <Modal visible={modalVisible} animationType="slide">
        <GenerateQRCode 
          onPress={() => setModalVisible(false)}
        />
      </Modal>
    </View>
  );
};

export default Home;

const styles = StyleSheet.create({
  welcomeContainer: {
    paddingVertical: 20,
    alignItems: "center",
    width: "100%",
  },
  greetingText: {
    fontSize: 18,
    fontWeight: "500",
    marginBottom: 5,
  },
  welcomeText: {
    fontSize: 24,
    fontWeight: "bold",
  },
  qrContainer: { 
    alignItems: "center", 
    marginTop: 10,
    marginBottom: 20,
  },
  qrImage: { 
    width: 150, 
    height: 150, 
    resizeMode: "contain",
  },
  featureContainer: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-around",
    width: "100%",
    marginBottom: 20,
  },
  card: {
    width: '30%',
    height: 120,
    borderRadius: 15,
    padding: 15,
    margin: 5,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  cardContent: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
  },
  cardText: {
    marginTop: 10,
    fontSize: 14,
    fontWeight: "bold",
    textAlign: "center",
  },
  premiumWrapper: {
    paddingHorizontal: 10,
    marginBottom: 30,
  },
  parkingTimerContainer: {
    marginVertical: 20,
    paddingHorizontal: 15,
  },
  parkingTimerCard: {
    backgroundColor: '#4361ee',
    borderRadius: 15,
    padding: 20,
    elevation: 5,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
  },
  parkingTimerContent: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  parkingTimerTextContainer: {
    flex: 1,
    marginLeft: 15,
  },
  parkingTimerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: '#FFF',
  },
  parkingTimerSubtitle: {
    fontSize: 14,
    color: '#FFF',
    opacity: 0.8,
  },
});
