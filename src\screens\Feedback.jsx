import React, { useContext, useState } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  StyleSheet,
  Alert,
} from "react-native";
import { AuthContext } from "../services/AuthContext";
import btn from "../styles/buttons";

const Feedback = ({ navigation }) => {
  const [rating, setRating] = useState(0);
  const [comment, setComment] = useState("");
  const { logout, user, setUser, theme, toggleTheme } = useContext(AuthContext);

  const mainBtn = btn(theme);

  const handleRatingPress = (selectedRating) => {
    setRating(selectedRating);
  };

  const handleSubmit = () => {
    if (rating === 0 || comment.trim() === "") {
      Alert.alert("Error", "Please provide a rating and a comment.");
      return;
    }

    // Static feedback submission logic
    Alert.alert(
      "Thank You!",
      `Your feedback has been submitted:\n\nRating: ${rating} stars\nComment: "${comment}"`
    );
    setRating(0);
    setComment("");
    navigation.navigate("Home"); // Navigate back to the Home screen
  };

  return (
    <View
      style={[styles.container, { backgroundColor: theme.backgroundColor }]}
    >
      <Text style={[styles.heading, {color: theme.primaryColor}]}>We value your feedback!</Text>
      <Text style={styles.subHeading}>Please rate your experience</Text>

      <View style={styles.ratingContainer}>
        {[1, 2, 3, 4, 5].map((star) => (
          <TouchableOpacity
            key={star}
            onPress={() => handleRatingPress(star)}
            style={[styles.star, rating >= star && styles.selectedStar]}
          >
            <Text
              style={[
                styles.starText,
                rating >= star && styles.selectedStarText,
              ]}
            >
              ★
            </Text>
          </TouchableOpacity>
        ))}
      </View>

      <TextInput
        style={styles.input}
        placeholder="Leave your comments here"
        multiline
        value={comment}
        onChangeText={setComment}
      />

      <TouchableOpacity style={mainBtn.appBtn} onPress={handleSubmit}>
        <Text style={styles.buttonText}>Submit Feedback</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    alignItems: "center",
  },
  heading: {
    fontSize: 24,
    fontWeight: "bold",
    marginVertical: 20,
  },
  subHeading: {
    fontSize: 16,
    color: "#6c757d",
    marginBottom: 20,
  },
  ratingContainer: {
    flexDirection: "row",
    justifyContent: "center",
    marginBottom: 20,
  },
  star: {
    marginHorizontal: 5,
  },
  starText: {
    fontSize: 30,
    color: "#ced4da",
  },
  selectedStar: {
    backgroundColor: "transparent",
  },
  selectedStarText: {
    color: "#ffc107",
  },
  input: {
    width: "100%",
    borderWidth: 1,
    borderColor: "#ced4da",
    borderRadius: 8,
    padding: 10,
    marginBottom: 20,
    backgroundColor: "#ffffff",
    textAlignVertical: "top",
  },
  button: {
    width: "100%",
    padding: 15,
    backgroundColor: "#008080",
    borderRadius: 8,
    alignItems: "center",
  },
  buttonText: {
    color: "#ffffff",
    fontSize: 16,
  },
});

export default Feedback;
