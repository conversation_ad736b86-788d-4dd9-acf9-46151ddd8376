import React from 'react';
import { View, StyleSheet } from 'react-native';
import { BannerAd, BannerAdSize, TestIds } from 'react-native-google-mobile-ads';

const AdBanner = () => {
  // Use test ID in development, real ID in production
  const adUnitId = __DEV__ 
    ? TestIds.BANNER 
    : 'ca-app-pub-4807931196810855/ACTUAL_BANNER_ID';

  return (
    <View style={styles.bannerContainer}>
      <BannerAd
        unitId={adUnitId}
        size={BannerAdSize.BANNER}
        requestOptions={{
          requestNonPersonalizedAdsOnly: true,
        }}
      />
    </View>
  );
};

const styles = StyleSheet.create({
  bannerContainer: {
    alignItems: 'center',
    marginVertical: 10,
    width: '100%'
  }
});

export default AdBanner;

