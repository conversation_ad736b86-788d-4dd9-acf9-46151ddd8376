import React, { useState, useRef, useContext } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  StyleSheet,
  Alert,
} from "react-native";
import * as Animatable from "react-native-animatable";
import Icon from "react-native-vector-icons/FontAwesome";
import btn from "../styles/buttons";
import { AuthContext } from "../services/AuthContext";
import { POST } from "../services/api";
import { URLS } from "../services/url";

const Login = ({ navigation }) => {
  const [phoneNumber, setPhoneNumber] = useState("");
  const [otp, setOtp] = useState(["", "", "", "", "", ""]); // OTP as an array of digits
  const [isOtpSent, setIsOtpSent] = useState(false);
  const { login } = useContext(AuthContext); // Get login function from context
  const userObject = {
    phone_number: phoneNumber,
  };
    const { logout, user, setUser, theme } = useContext(AuthContext);
    const mainBtn = btn(theme)

  const inputRefs = useRef([]);

  const handleSendOtp = async () => {
    // let response = await POST(URLS.login, userObject);
    let response = {
      otp: 123456
    }
    setIsOtpSent(true);
    setOtp(response?.otp.split(""))
  };

  const handleVerifyOtp = async () => {
    const enteredOtp = otp.join("");
    try {
      await login({...userObject, otp: enteredOtp});
      Alert.alert("Login Successful", "You are logged in!");
      // navigation.navigate("Home");
    } catch (error) {
      console.log("Error saving user data:", error);
    }
  };

  const handleOtpChange = (value, index) => {
    const updatedOtp = [...otp];
    updatedOtp[index] = value;

    setOtp(updatedOtp);

    if (value && index < inputRefs.current.length - 1) {
      inputRefs.current[index + 1].focus();
    }
  };

  const handleBackspace = (value, index) => {
    if (!value && index > 0) {
      inputRefs.current[index - 1].focus();
    }
  };

  const checkInput = (e) => {
    if (e?.length <= 10) {
      setPhoneNumber(e);
    }
  };



  return (
    <View style={[styles.container, {backgroundColor: theme.backgroundColor}]}>
      <Animatable.View
        animation="bounceIn"
        delay={500}
        style={styles.logoContainer}
      >
        <Image
          source={require("../Assets/qrLogo.png")} // QR logo
          style={styles.logo}
        />
      </Animatable.View>

      <Animatable.View
        animation="fadeInUp"
        delay={1000}
        style={styles.textContainer}
      >
        <Text style={styles.title}>Welcome Back</Text>
        <Text style={styles.subtitle}>Login to manage your parking</Text>
      </Animatable.View>

      {!isOtpSent ? (
        <>
          <View style={styles.inputContainer}>
            <View style={styles.inputWrapper}>
              <View style={styles.icon_container}>
                <Icon
                  name="phone"
                  size={20}
                  color="#4e54c8"
                  style={styles.inputIcon}
                />
              </View>

              <TextInput
                placeholder="Enter Phone Number"
                style={styles.input}
                placeholderTextColor="#aaa"
                keyboardType="phone-pad"
                value={phoneNumber}
                onChangeText={(e) => checkInput(e)}
                maxLength={10}
              />
            </View>
            <TouchableOpacity style={mainBtn.login} onPress={handleSendOtp}>
              <Text style={mainBtn.text}>Get OTP</Text>
            </TouchableOpacity>
          </View>

          {/* Registration Options */}
          <Animatable.View
            animation="fadeInUp"
            delay={1500}
            style={styles.registerContainer}
          >
            <Text style={styles.registerText}>New here? Register:</Text>

            <TouchableOpacity
              style={mainBtn.reg_qr}
              onPress={() =>
                Alert.alert("Registration", "Scan QR Code to register.")
              }
            >
              <Text style={mainBtn.text}>Register with QR Code</Text>
            </TouchableOpacity>

            <TouchableOpacity
              style={mainBtn.reg_man}
              onPress={() => navigation.navigate("SignUp")}
            >
              <Text style={mainBtn.text}>Register Manually</Text>
            </TouchableOpacity>
          </Animatable.View>
        </>
      ) : (
        // OTP Input
        <View style={styles.inputContainer}>
          <Text style={styles.otpLabel}>Enter OTP:</Text>
          <View style={styles.otpContainer}>
            {otp?.map((digit, index) => (
              <TextInput
                key={index}
                ref={(ref) => (inputRefs.current[index] = ref)} // Assign refs
                style={styles.otpInput}
                keyboardType="numeric"
                maxLength={1}
                value={digit}
                onChangeText={(value) => handleOtpChange(value, index)}
                onKeyPress={({ nativeEvent }) =>
                  nativeEvent.key === "Backspace" &&
                  handleBackspace(digit, index)
                }
              />
            ))}
          </View>
          <TouchableOpacity style={mainBtn.login} onPress={handleVerifyOtp}>
            <Text style={mainBtn.text}>Verify OTP</Text>
          </TouchableOpacity>
        </View>
      )}
    </View>
  );
};

export default Login;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    // backgroundColor: "#fff",
    justifyContent: "center",
    alignItems: "center",
  },

  logoContainer: {
    alignItems: "center",
    marginTop: 50,
  },

  logo: {
    width: 120,
    height: 120,
    resizeMode: "contain",
  },

  textContainer: {
    alignItems: "center",
    marginTop: 20,
  },

  title: {
    fontSize: 28,
    fontWeight: "bold",
    color: "#4e54c8",
  },

  subtitle: {
    fontSize: 16,
    color: "#666",
    marginTop: 10,
  },

  inputContainer: {
    marginTop: 30,
    justifyContent: "center",
    alignItems: "center",
    gap: 15,
    position: "relative",
  },

  input: {
    flex: 1,
    borderWidth: 1, // Add the border
    borderColor: "#ccc", // Border color
    borderRadius: 10, // Rounded corners (optional)
    paddingHorizontal: 35, // Padding for text inside the input
    backgroundColor: "#fff", // Optional: background color
    fontSize: 17,
  },

  icon_container: {
    height: "100%",
    zIndex: 99,
    position: "absolute",
    width: 30,
    justifyContent: "center",
    alignItems: "center",
  },

  inputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    width: "80%",
    gap: 10,
  },

  otpLabel: {
    fontSize: 18,
    fontWeight: "bold",
    color: "#4e54c8",
    textAlign: "center",
    marginBottom: 10,
  },

  otpContainer: {
    flexDirection: "row",
    justifyContent: "space-between",
    width: "80%",
  },

  otpInput: {
    width: 44,
    height: 50,
    borderRadius: 10,
    borderWidth: 1,
    borderColor: "#ddd",
    backgroundColor: "#f5f5f5",
    textAlign: "center",
    fontSize: 18,
    color: "#333",
  },

  registerContainer: {
    marginTop: 40,
    alignItems: "center",
    gap: 15,
  },

  registerText: {
    fontSize: 16,
    color: "#666",
    marginBottom: 20,
  },
});
