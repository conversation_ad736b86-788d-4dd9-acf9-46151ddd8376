import React, { useContext, useState } from "react";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Switch,
  ScrollView,
} from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
import globalStyle from "../styles/global";
import { AuthContext } from "../services/AuthContext";

const Settings = ({ navigation }) => {
  const { theme, toggleTheme } = useContext(AuthContext);

  const [settings, setSettings] = useState({
    notificationsEnabled: true,
  });

  const toggleSetting = (key) => {
    setSettings((prev) => ({ ...prev, [key]: !prev[key] }));
  };

  const sections = [
    {
      title: "App Preferences",
      options: [
        {
          label: "Notifications",
          icon: "bell-outline",
          type: "switch",
          stateKey: "notificationsEnabled",
        },
        {
          label: "Dark Theme",
          icon: "palette-outline",
          action: () => toggleTheme(),
        },
      ],
    },
    {
      title: "Support & About",
      options: [
        {
          label: "Support & About",
          icon: "information",
          action: () => navigation.navigate("SupportAbout"),
        },
        {
          label: "Give Feedback",
          icon: "message-text",
          action: () => navigation.navigate("Feedback"),
        },
      ],
    },
  ];

  const mainPage = globalStyle(theme);

  return (
    <View style={[mainPage.container, styles.container]}>
      <Text style={[styles.header, { color: theme.primaryColor }]}>
        Settings
      </Text>
      <ScrollView
        contentContainerStyle={styles.contentContainer}
        showsVerticalScrollIndicator={false}
      >
        {sections.map((section, index) => (
          <View
            key={index}
            style={[styles.section, { backgroundColor: theme.cardColor }]}
          >
            <Text style={[styles.sectionTitle, { color: theme.primaryColor }]}>
              {section.title}
            </Text>
            {section.options.map((option, idx) => (
              <View key={idx} style={styles.settingItem}>
                <View style={styles.settingLeft}>
                  <Icon
                    name={option.icon}
                    size={24}
                    color={theme.primaryColor}
                  />
                  <Text
                    style={[styles.settingText, { color: theme.textColor }]}
                  >
                    {option.label}
                  </Text>
                </View>
                {option.type === "switch" ? (
                  <Switch
                    value={settings[option.stateKey]}
                    onValueChange={() => toggleSetting(option.stateKey)}
                    trackColor={{ false: "#767577", true: theme.primaryColor }}
                    thumbColor={settings[option.stateKey] ? "#fff" : "f4f3f4"}
                  />
                ) : (
                  option.action && (
                    <TouchableOpacity onPress={option.action}>
                      <Icon
                        name="chevron-right"
                        size={20}
                        color={theme.primaryColor}
                      />
                    </TouchableOpacity>
                  )
                )}
              </View>
            ))}
          </View>
        ))}


      </ScrollView>
    </View>
  );
};

export default Settings;

const styles = StyleSheet.create({
  contentContainer: { paddingBottom: 50, minWidth: "90%" },
  header: {
    fontSize: 24,
    fontWeight: "bold",
    textAlign: "center",
    marginVertical: 15,
  },
  section: {
    borderRadius: 12,
    padding: 15,
    width: "100%",
    marginBottom: 15,
    elevation: 2,
    shadowColor: "#000",
    shadowOpacity: 0.1,
    shadowOffset: { width: 0, height: 2 },
    shadowRadius: 4,
  },
  sectionTitle: {
    fontSize: 16,
    fontWeight: "bold",
    marginBottom: 10,
  },
  settingItem: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: "#ddd",
  },
  settingLeft: { flexDirection: "row", alignItems: "center", gap: 10 },
  settingText: { fontSize: 16 },
  feedbackButton: {
    flexDirection: "row",
    paddingVertical: 14,
    borderRadius: 10,
    alignItems: "center",
    justifyContent: "center",
    marginTop: 20,
    gap: 10,
  },
  feedbackButtonText: { color: "#fff", fontSize: 16, fontWeight: "bold" },
});
