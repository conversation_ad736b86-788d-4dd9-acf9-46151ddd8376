import React from "react";
import { StyleSheet, Text, TouchableOpacity, Alert } from "react-native";
import { View } from "react-native-animatable";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";


export default function PremiumCard({ navigation }) {
  const handleUpgrade = () => {
    // Navigate to premium subscription screen
    // If not implemented yet, show an alert
    Alert.alert(
      "Premium Feature",
      "Premium subscription will be available soon!",
      [{ text: "OK" }]
    );
  };

  return (
    <View style={styles.premiumContainer}>
      <View style={styles.premiumHeader}>
        <Icon
          name="crown"
          size={50}
          color="#FFD700"
          style={styles.premiumIcon}
        />
        <Text style={styles.premiumTitle}>Go Premium</Text>
      </View>
      <Text style={styles.premiumText}>
        🚀 Unlock exclusive features, unlimited QR codes, and priority support!
      </Text>
      <Text style={styles.premiumBenefits}>
        ✔ Unlimited QR Codes ✔ Priority Support ✔ Exclusive Offers
      </Text>
      <TouchableOpacity
        style={styles.premiumButton}
        onPress={handleUpgrade}
      >
        <Text style={styles.premiumButtonText}>Upgrade Now</Text>
      </TouchableOpacity>
    </View>
  );
}

const styles = StyleSheet.create({
  premiumContainer: {
    marginTop: 30,
    padding: 25,
    backgroundColor: "#FFF5CC",
    borderRadius: 15,
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.2,
    shadowRadius: 6,
    elevation: 5,
    borderWidth: 2,
    borderColor: "#FFD700",
  },
  premiumHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 10,
  },
  premiumIcon: { marginRight: 10 },
  premiumTitle: { fontSize: 24, fontWeight: "bold", color: "#DAA520" },
  premiumText: {
    fontSize: 14,
    color: "#555",
    textAlign: "center",
    marginVertical: 10,
    paddingHorizontal: 10,
    fontStyle: "italic",
  },
  premiumBenefits: {
    fontSize: 14,
    color: "#444",
    textAlign: "center",
    fontWeight: "bold",
    marginBottom: 10,
  },
  premiumButton: {
    backgroundColor: "#DAA520",
    paddingVertical: 14,
    paddingHorizontal: 20,
    borderRadius: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
  },
  premiumButtonText: { color: "white", fontSize: 18, fontWeight: "bold" },
});
