// theme.js
const lightTheme = {
    background: "#F5F5F5",
    text: "#333",
    card: "#FFF",
    border: "#ddd",
    buttonBackground: "#007bff",
    buttonText: "#fff",
  };
  
  const darkTheme = {
    background: "#121212",
    text: "#FFF",
    card: "#1E1E1E",
    border: "#333",
    buttonBackground: "#BB86FC",
    buttonText: "#000",
  };
  
  const getTheme = (isDarkMode) => (isDarkMode ? darkTheme : lightTheme);
  
  export { lightTheme, darkTheme, getTheme };
  