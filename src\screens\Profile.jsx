import React, { useContext, useState, useEffect } from "react";
import { useNavigation, useFocusEffect } from "@react-navigation/native";
import {
  View,
  Text,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
  ScrollView,
  StatusBar,
} from "react-native";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
import { AuthContext } from "../services/AuthContext";
import Card from "../components/Card";
import globalStyle from "../styles/global";

const Profile = () => {
  const { logout, user, setUser, theme } = useContext(AuthContext);
  const navigation = useNavigation();
  const mainPage = globalStyle(theme);

  useFocusEffect(
    React.useCallback(() => {
      const fetchUserData = async () => {
        try {
          const storedUser = await AsyncStorage.getItem("user");
          if (storedUser) {
            const parsedUser = JSON.parse(storedUser);
            setUser(parsedUser);
          }
        } catch (error) {
          console.log("Error fetching user data");
        }
      };
      fetchUserData();
    }, [])
  );

  const handleLogout = () => {
    Alert.alert("Logout", "Are you sure you want to log out?", [
      { text: "Cancel", style: "cancel" },
      { text: "Logout", onPress: () => logout() },
    ]);
  };

  return (
    <View style={[mainPage.container, styles.container]}>
      <StatusBar backgroundColor={theme.backgroundColor} barStyle={theme.isLight ? "dark-content" : "light-content"} />
      
      <ScrollView showsVerticalScrollIndicator={false} contentContainerStyle={styles.scrollContent}>
        {/* Profile Header */}
        <View style={styles.header}>
          <View style={styles.avatarContainer}>
            <Image
              source={{
                uri: user?.profilePic || "https://via.placeholder.com/100",
              }}
              style={styles.avatar}
            />
            <TouchableOpacity 
              style={styles.editIconContainer}
              onPress={() => navigation.navigate("EditProfile")}
              activeOpacity={0.7}
            >
              <Icon name="pencil" size={16} color="#FFF" />
            </TouchableOpacity>
          </View>
          
          <Text style={[styles.headerText, { color: theme.textColor }]}>
            {user?.name || "No name available"}
          </Text>
          <Text style={[styles.subHeaderText, { color: theme.textColor + '99' }]}>
            {user?.email || "No email available"}
          </Text>
        </View>

        {/* Stats Cards */}
        <View style={mainPage.featureContainer}>
          <Card
            name={"qrcode"}
            count={24}
            countTitle={"QR Scans"}
            onPress={() => {}}
            color={"#4CAF50"}
          />
          <Card
            name={"car"}
            count={20}
            countTitle={"Parking History"}
            onPress={() => {}}
            color={"#008080"}
          />
          <Card
            name={"gift"}
            count={10}
            countTitle={"Rewards"}
            onPress={() => {}}
            color={"#FFD700"}
          />
        </View>

        {/* Settings */}
        <View style={styles.settingsContainer}>
          <TouchableOpacity
            style={[styles.settingItem, { backgroundColor: theme.cardColor }]}
            activeOpacity={0.7}
          >
            <View style={styles.settingIconWrapper}>
              <Icon name="lock" size={22} color="#2196F3" />
            </View>
            <Text style={[styles.settingText, { color: theme.textColor }]}>
              Change Password
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.settingItem, { backgroundColor: theme.cardColor }]}
            activeOpacity={0.7}
          >
            <View style={styles.settingIconWrapper}>
              <Icon name="bookmark" size={22} color="#FF9800" />
            </View>
            <Text style={[styles.settingText, { color: theme.textColor }]}>
              Saved Parking Spots
            </Text>
          </TouchableOpacity>
          
          <TouchableOpacity
            style={[styles.settingItem, { backgroundColor: theme.cardColor }]}
            activeOpacity={0.7}
          >
            <View style={styles.settingIconWrapper}>
              <Icon name="share-variant" size={22} color="#4CAF50" />
            </View>
            <Text style={[styles.settingText, { color: theme.textColor }]}>
              Refer & Earn
            </Text>
          </TouchableOpacity>
        </View>

        {/* Logout Button */}
        <TouchableOpacity
          style={styles.logoutButton}
          onPress={handleLogout}
          activeOpacity={0.8}
        >
          <Icon name="logout" size={20} color="#FFF" />
          <Text style={styles.logoutText}>Logout</Text>
        </TouchableOpacity>
      </ScrollView>
    </View>
  );
};

export default Profile;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    paddingHorizontal: 15,
  },
  scrollContent: {
    paddingBottom: 30,
  },
  header: { 
    alignItems: "center", 
    marginBottom: 25,
    paddingTop: 15,
  },
  avatarContainer: {
    position: "relative",
    marginBottom: 15,
  },
  avatar: {
    width: 100,
    height: 100,
    borderRadius: 50,
    borderWidth: 3,
    borderColor: "#4e54c8",
  },
  editIconContainer: {
    position: "absolute",
    bottom: 0,
    right: 0,
    backgroundColor: "#4e54c8",
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: "center",
    alignItems: "center",
    borderWidth: 2,
    borderColor: "white",
  },
  headerText: { 
    fontSize: 24, 
    fontWeight: "bold",
    marginBottom: 5,
  },
  subHeaderText: { 
    fontSize: 16,
  },
  settingsContainer: { 
    width: "100%", 
    paddingHorizontal: 5, 
    marginTop: 20,
    marginBottom: 20 
  },
  settingItem: {
    flexDirection: "row",
    alignItems: "center",
    padding: 16,
    borderRadius: 12,
    marginBottom: 12,
    elevation: 2,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 1 },
    shadowOpacity: 0.1,
    shadowRadius: 2,
  },
  settingIconWrapper: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.05)",
    marginRight: 15,
  },
  settingText: { 
    fontSize: 16,
    fontWeight: "500",
  },
  logoutButton: {
    flexDirection: "row",
    alignItems: "center",
    justifyContent: "center",
    marginHorizontal: 20,
    marginTop: 10,
    paddingVertical: 14,
    borderRadius: 12,
    backgroundColor: "#FF3B30",
    elevation: 3,
    shadowColor: "#FF3B30",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 3,
  },
  logoutText: {
    color: "#FFF",
    fontSize: 16,
    fontWeight: "600",
    marginLeft: 10,
  },
});
