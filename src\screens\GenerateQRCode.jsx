import React, { useContext, useState, useRef } from "react";
import { 
  View, 
  Text, 
  TextInput, 
  TouchableOpacity, 
  StyleSheet, 
  Alert,
  ScrollView,
  KeyboardAvoidingView,
  Platform,
  Animated,
  Keyboard
} from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
import { useNavigation } from "@react-navigation/native";
import { AuthContext } from "../services/AuthContext";
import btn from "../styles/buttons";
import BackButton from "../components/BackButton";

const GenerateQRCode = ({ onPress }) => {
  const navigation = useNavigation();
  const [formData, setFormData] = useState({
    name: "",
    flatNumber: "",
    licensePlate: "",
    contactNumber: ""
  });
  const [errors, setErrors] = useState({});
  const [activeField, setActiveField] = useState(null);
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(30)).current;
  
  // Input refs for focus handling
  const nameRef = useRef(null);
  const flatNumberRef = useRef(null);
  const contactNumberRef = useRef(null);
  const licensePlateRef = useRef(null);

  const { theme } = useContext(AuthContext);
  const mainBtn = btn(theme);

  // Animate form on mount
  React.useEffect(() => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true
      })
    ]).start();
  }, []);

  const validateField = (name, value) => {
    let newErrors = {...errors};
    
    switch(name) {
      case 'name':
        if (!value.trim()) newErrors.name = "Name is required";
        else delete newErrors.name;
        break;
      case 'flatNumber':
        if (!value.trim()) newErrors.flatNumber = "Flat/House number is required";
        else delete newErrors.flatNumber;
        break;
      case 'licensePlate':
        if (!value.trim()) newErrors.licensePlate = "License plate is required";
        else delete newErrors.licensePlate;
        break;
      case 'contactNumber':
        if (!value.trim()) newErrors.contactNumber = "Contact number is required";
        else if (!/^\d{10}$/.test(value.replace(/[^0-9]/g, ''))) 
          newErrors.contactNumber = "Please enter a valid 10-digit number";
        else delete newErrors.contactNumber;
        break;
    }
    
    setErrors(newErrors);
  };

  const handleChange = (name, value) => {
    setFormData(prev => ({...prev, [name]: value}));
  };

  const handleBlur = (name) => {
    validateField(name, formData[name]);
    setActiveField(null);
  };

  const handleFocus = (name) => {
    setActiveField(name);
  };

  const handleSubmit = () => {
    // Validate form data
    if (!formData.name || !formData.contactNumber) {
      Alert.alert("Error", "Name and contact number are required");
      return;
    }
    
    // Format phone number if needed
    const phoneRegex = /^\d{10}$/;
    if (!phoneRegex.test(formData.contactNumber.replace(/[^0-9]/g, ''))) {
      Alert.alert("Error", "Please enter a valid 10-digit phone number");
      return;
    }
    
    // Create properly formatted data for QR code
    const userData = {
      firstName: formData.name.split(' ')[0],
      lastName: formData.name.split(' ').slice(1).join(' '),
      email: '',
      address: formData.flatNumber || '',
      contactNumber: formData.contactNumber.replace(/[^0-9]/g, ''),
      licensePlate: formData.licensePlate || ''
    };
    
    console.log("Generated QR data:", userData); // Add debug log
    
    // Navigate to QR display with the data
    navigation.navigate("QRDisplay", { userData });
  };

  return (
    <KeyboardAvoidingView
      behavior={Platform.OS === "ios" ? "padding" : "height"}
      style={[styles.container, { backgroundColor: theme.backgroundColor }]}
    >
      {/* Using the reusable BackButton component */}
      <BackButton onPress={onPress} title="Back" />

      <ScrollView 
        showsVerticalScrollIndicator={false}
        contentContainerStyle={styles.scrollContent}
        keyboardShouldPersistTaps="handled"
      >
        <Animated.View style={[
          styles.formContainer,
          { opacity: fadeAnim, transform: [{ translateY: slideAnim }] }
        ]}>
          <View style={styles.headerContainer}>
            <Text style={[styles.header, {color: theme.primaryColor}]}>Car Contact QR Code</Text>
            <Text style={[styles.subheader, {color: theme.textColor}]}>
              Create a QR code for your car with privacy protection
            </Text>
          </View>

          {/* Resident Information */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Icon name="home-account" size={20} color={theme.primaryColor} />
              <Text style={[styles.sectionTitle, {color: theme.textColor}]}>Resident Information</Text>
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.label, {color: theme.textColor}]}>Your Name</Text>
              <Text style={[styles.sublabel, {color: theme.textColor}]}>
                Only your first name will be visible to others
              </Text>
              <TextInput
                ref={nameRef}
                style={[
                  styles.input, 
                  activeField === 'name' && styles.activeInput,
                  errors.name && styles.errorInput
                ]}
                placeholder="Enter your name"
                placeholderTextColor="#999"
                value={formData.name}
                onChangeText={(text) => handleChange('name', text)}
                onFocus={() => handleFocus('name')}
                onBlur={() => handleBlur('name')}
                returnKeyType="next"
                onSubmitEditing={() => flatNumberRef.current?.focus()}
              />
              {errors.name && <Text style={styles.errorText}>{errors.name}</Text>}
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.label, {color: theme.textColor}]}>Flat/House Number</Text>
              <TextInput
                ref={flatNumberRef}
                style={[
                  styles.input, 
                  activeField === 'flatNumber' && styles.activeInput,
                  errors.flatNumber && styles.errorInput
                ]}
                placeholder="e.g. A-101"
                placeholderTextColor="#999"
                value={formData.flatNumber}
                onChangeText={(text) => handleChange('flatNumber', text)}
                onFocus={() => handleFocus('flatNumber')}
                onBlur={() => handleBlur('flatNumber')}
                returnKeyType="next"
                onSubmitEditing={() => contactNumberRef.current?.focus()}
              />
              {errors.flatNumber && <Text style={styles.errorText}>{errors.flatNumber}</Text>}
            </View>
          </View>

          {/* Private Contact Information */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Icon name="shield-account" size={20} color={theme.primaryColor} />
              <Text style={[styles.sectionTitle, {color: theme.textColor}]}>Private Contact Details</Text>
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.label, {color: theme.textColor}]}>Contact Number</Text>
              <View style={styles.privacyBadge}>
                <Icon name="shield-check" size={16} color="#4CAF50" />
                <Text style={styles.privacyText}>Your number is protected</Text>
              </View>
              <Text style={[styles.sublabel, {color: theme.textColor}]}>
                Your number will be securely stored but never directly shared
              </Text>
              <TextInput
                ref={contactNumberRef}
                style={[
                  styles.input, 
                  activeField === 'contactNumber' && styles.activeInput,
                  errors.contactNumber && styles.errorInput
                ]}
                placeholder="Your mobile number"
                placeholderTextColor="#999"
                value={formData.contactNumber}
                onChangeText={(text) => handleChange('contactNumber', text)}
                onFocus={() => handleFocus('contactNumber')}
                onBlur={() => handleBlur('contactNumber')}
                keyboardType="phone-pad"
                returnKeyType="next"
                onSubmitEditing={() => licensePlateRef.current?.focus()}
              />
              {errors.contactNumber && <Text style={styles.errorText}>{errors.contactNumber}</Text>}
            </View>
          </View>

          {/* Vehicle Information */}
          <View style={styles.section}>
            <View style={styles.sectionHeader}>
              <Icon name="car" size={20} color={theme.primaryColor} />
              <Text style={[styles.sectionTitle, {color: theme.textColor}]}>Vehicle Information</Text>
            </View>

            <View style={styles.inputContainer}>
              <Text style={[styles.label, {color: theme.textColor}]}>License Plate Number</Text>
              <TextInput
                ref={licensePlateRef}
                style={[
                  styles.input, 
                  activeField === 'licensePlate' && styles.activeInput,
                  errors.licensePlate && styles.errorInput
                ]}
                placeholder="e.g. MH01AB1234"
                placeholderTextColor="#999"
                value={formData.licensePlate}
                onChangeText={(text) => handleChange('licensePlate', text.toUpperCase())}
                onFocus={() => handleFocus('licensePlate')}
                onBlur={() => handleBlur('licensePlate')}
                autoCapitalize="characters"
                returnKeyType="done"
                onSubmitEditing={() => Keyboard.dismiss()}
              />
              {errors.licensePlate && <Text style={styles.errorText}>{errors.licensePlate}</Text>}
            </View>
          </View>

          <View style={styles.infoBox}>
            <Icon name="information-outline" size={20} color={theme.primaryColor} />
            <Text style={[styles.infoText, {color: theme.textColor}]}>
              When someone scans your QR code, they can request you to move your car without seeing your phone number.
            </Text>
          </View>

          <View style={styles.buttonContainer}>
            <TouchableOpacity 
              style={[mainBtn.appBtn, styles.generateButton]} 
              onPress={handleSubmit}
              activeOpacity={0.8}
            >
              <Text style={styles.buttonText}>Generate Secure QR Code</Text>
              <Icon name="qrcode-scan" size={20} color="white" />
            </TouchableOpacity>
          </View>
        </Animated.View>
      </ScrollView>
    </KeyboardAvoidingView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingVertical: 20,
    paddingHorizontal: 16,
  },
  formContainer: {
    borderRadius: 16,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: "transparent",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  headerContainer: {
    marginBottom: 25,
    alignItems: "center",
  },
  header: {
    fontSize: 28,
    fontWeight: "bold",
    marginBottom: 8,
    textAlign: "center",
  },
  subheader: {
    fontSize: 16,
    textAlign: "center",
    opacity: 0.8,
  },
  section: {
    marginBottom: 20,
    backgroundColor: 'rgba(255,255,255,0.05)',
    borderRadius: 16,
    padding: 16,
    borderWidth: 1,
    borderColor: '#e0e0e0',
    shadowColor: "transparent",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  sectionHeader: {
    flexDirection: "row",
    alignItems: "center",
    marginBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
    paddingBottom: 10,
  },
  sectionTitle: {
    fontSize: 18,
    fontWeight: "600",
    marginLeft: 8,
  },
  inputContainer: {
    marginBottom: 16,
  },
  label: {
    fontSize: 16,
    fontWeight: "500",
    marginBottom: 8,
  },
  sublabel: {
    fontSize: 14,
    marginBottom: 8,
    opacity: 0.7,
  },
  input: {
    backgroundColor: "white",
    padding: 16,
    borderRadius: 12,
    fontSize: 16,
    borderWidth: 1,
    borderColor: "#ddd",
  },
  activeInput: {
    borderColor: "#4e54c8",
    borderWidth: 2,
    shadowColor: "transparent",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  errorInput: {
    borderColor: "#FF3B30",
  },
  errorText: {
    color: "#FF3B30",
    fontSize: 12,
    marginTop: 5,
    marginLeft: 5,
  },
  privacyBadge: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "rgba(76, 175, 80, 0.1)",
    paddingVertical: 4,
    paddingHorizontal: 8,
    borderRadius: 12,
    alignSelf: "flex-start",
    marginBottom: 8,
  },
  privacyText: {
    color: "#4CAF50",
    fontSize: 12,
    fontWeight: "500",
    marginLeft: 4,
  },
  infoBox: {
    flexDirection: "row",
    backgroundColor: 'rgba(78, 84, 200, 0.1)',
    borderRadius: 12,
    padding: 12,
    marginBottom: 20,
    alignItems: "center",
    borderWidth: 1,
    borderColor: 'rgba(78, 84, 200, 0.2)',
    shadowColor: "transparent",
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0,
    shadowRadius: 0,
    elevation: 0,
  },
  infoText: {
    fontSize: 14,
    marginLeft: 10,
    flex: 1,
  },
  buttonContainer: {
    marginTop: 10,
    marginBottom: 20,
  },
  generateButton: {
    flexDirection: "row",
    justifyContent: "center",
    alignItems: "center",
    paddingVertical: 16,
    borderRadius: 12,
  },
  buttonText: {
    color: "white",
    fontSize: 18,
    fontWeight: "bold",
    marginRight: 10,
  },
});

export default GenerateQRCode;
