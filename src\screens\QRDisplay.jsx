import React, { useContext } from "react";
import { View, Text, StyleSheet, TouchableOpacity } from "react-native";
import QRCode from "react-native-qrcode-svg";
import Icon from "react-native-vector-icons/FontAwesome";
import { AuthContext } from "../services/AuthContext";
import btn from "../styles/buttons";

const QRDisplay = ({ route, navigation }) => {
  const { userData } = route.params;

  const { logout, user, setUser, theme, toggleTheme } = useContext(AuthContext);

  const mainBtn = btn(theme)

  return (
    <View
      style={[styles.container, { backgroundColor: theme.backgroundColor }]}
    >
      <Text style={[styles.header, { color: theme.primaryColor }]}>
        Your Parking QR Code
      </Text>

      <View style={styles.qrContainer}>
        <QRCode
          value={JSON.stringify(userData)}
          size={250}
          color={theme.primaryColor}
          backgroundColor="white"
          // Add error correction level for better scanning
          ecl="M"
        />
      </View>

      <Text style={styles.userInfo}>
        {userData.firstName} {userData.lastName}
      </Text>
      <Text style={styles.userEmail}>{userData.email}</Text>
      <Text style={styles.userAddress}>{userData.address}</Text>

      <TouchableOpacity
        style={mainBtn.appBtn}
        onPress={() => navigation.goBack()}
      >
        <Icon name="arrow-left" size={18} color="#008080" />
        <Text style={mainBtn.text}>Back to Form</Text>
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    padding: 20,
    alignItems: "center",
  },
  header: {
    fontSize: 24,
    fontWeight: "bold",
    marginVertical: 30,
  },
  qrContainer: {
    backgroundColor: "white",
    padding: 20,
    borderRadius: 20,
    marginBottom: 20,
  },
  userInfo: {
    fontSize: 20,
    fontWeight: "600",
    color: "#333",
    marginBottom: 5,
  },
  userEmail: {
    fontSize: 16,
    color: "#666",
    marginBottom: 5,
  },
  userAddress: {
    fontSize: 16,
    fontWeight: "500",
    color: "#444",
    marginBottom: 30,
    textAlign: "center",
  },
  backButton: {
    flexDirection: "row",
    alignItems: "center",
    padding: 15,
  },
  backButtonText: {
    color: "#008080",
    fontSize: 16,
    marginLeft: 10,
    fontWeight: "600",
  },
});

export default QRDisplay;
