import React, { useState, useEffect } from "react";
import {
  View,
  Text,
  TextInput,
  TouchableOpacity,
  Image,
  StyleSheet,
  Alert,
} from "react-native";
import Icon from "react-native-vector-icons/FontAwesome";

const SignUp = ({ navigation }) => {
  const [username, setUsername] = useState("");
  const [phoneNumber, setPhoneNumber] = useState("");
  const [email, setEmail] = useState("");
  const [password, setPassword] = useState("");
  const [isValid, setIsValid] = useState(false);

  // Validation function
  useEffect(() => {
    const phoneRegex = /^[0-9]{10}$/;
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;

    if (
      username.trim().length >= 3 &&
      phoneRegex.test(phoneNumber) &&
      emailRegex.test(email) &&
      password.trim().length >= 6
    ) {
      setIsValid(true);
    } else {
      setIsValid(false);
    }
  }, [username, phoneNumber, email, password]);

  const handleSignUp = () => {
    Alert.alert("Success", "Account created successfully!");
    navigation.navigate("Login");
  };

  return (
    <View style={styles.container}>
      {/* QR Themed Logo */}
      <View style={styles.logoContainer}>
        <Image
          source={require("../Assets/qrLogo.png")} // QR-themed logo
          style={styles.logo}
        />
      </View>

      {/* Header Text */}
      <View style={styles.headerContainer}>
        <Text style={styles.headerText}>Create Account</Text>
        <Text style={styles.subHeaderText}>Sign up to get started!</Text>
      </View>

      {/* Input Fields */}
      <View style={styles.inputContainer}>
        <View style={styles.inputWrapper}>
          <Icon name="user" size={20} color="#888" style={styles.inputIcon} />
          <TextInput
            placeholder="Username"
            style={styles.input}
            placeholderTextColor="#888"
            value={username}
            onChangeText={setUsername}
          />
        </View>
        <View style={styles.inputWrapper}>
          <Icon name="phone" size={20} color="#888" style={styles.inputIcon} />
          <TextInput
            placeholder="Phone Number"
            keyboardType="phone-pad"
            style={styles.input}
            placeholderTextColor="#888"
            value={phoneNumber}
            onChangeText={setPhoneNumber}
          />
        </View>
        <View style={styles.inputWrapper}>
          <Icon name="envelope" size={20} color="#888" style={styles.inputIcon} />
          <TextInput
            placeholder="Email"
            keyboardType="email-address"
            style={styles.input}
            placeholderTextColor="#888"
            value={email}
            onChangeText={setEmail}
          />
        </View>
        <View style={styles.inputWrapper}>
          <Icon name="lock" size={20} color="#888" style={styles.inputIcon} />
          <TextInput
            placeholder="Password"
            secureTextEntry
            style={styles.input}
            placeholderTextColor="#888"
            value={password}
            onChangeText={setPassword}
          />
        </View>
      </View>

      {/* Sign Up Button */}
      <TouchableOpacity
        style={[styles.signUpButton, { backgroundColor: isValid ? "#4CAF50" : "#A5D6A7" }]}
        onPress={handleSignUp}
        disabled={!isValid}
      >
        <Text style={styles.signUpButtonText}>Sign Up</Text>
      </TouchableOpacity>

      {/* Already Have an Account */}
      <TouchableOpacity onPress={() => navigation.navigate("Login")}>
        <Text style={styles.haveAccountText}>
          Already have an account? <Text style={styles.signInText}>Sign In</Text>
        </Text>
      </TouchableOpacity>
    </View>
  );
};

export default SignUp;

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: "#F5F5F5",
    paddingHorizontal: 20,
  },
  logoContainer: {
    alignItems: "center",
    marginTop: 50,
  },
  logo: {
    width: 120,
    height: 120,
    resizeMode: "contain",
  },
  headerContainer: {
    alignItems: "center",
    marginTop: 20,
  },
  headerText: {
    fontSize: 24,
    fontWeight: "bold",
    color: "#333",
  },
  subHeaderText: {
    fontSize: 16,
    color: "#666",
    marginTop: 10,
  },
  inputContainer: {
    marginTop: 30,
  },
  inputWrapper: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFF",
    borderRadius: 10,
    paddingHorizontal: 10,
    marginBottom: 20,
    borderWidth: 1,
    borderColor: "#ddd",
    height: 50,
  },
  inputIcon: {
    marginRight: 10,
  },
  input: {
    flex: 1,
    fontSize: 16,
    color: "#262626",
  },
  signUpButton: {
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: "center",
    marginTop: 20,
  },
  signUpButtonText: {
    color: "#FFF",
    fontSize: 18,
    fontWeight: "600",
  },
  haveAccountText: {
    color: "#888",
    textAlign: "center",
    marginTop: 20,
    fontSize: 16,
  },
  signInText: {
    color: "#4CAF50",
    fontWeight: "600",
  },
});
