// import React from "react";
// import { createBottomTabNavigator } from "@react-navigation/bottom-tabs";
// import { createNativeStackNavigator } from "@react-navigation/native-stack";
// import { View, TouchableOpacity } from "react-native";
// import Icon from "react-native-vector-icons/FontAwesome";

// // Import Screens
// import Home from "../screens/Home";
// import ScanQRCode from "../screens/ScanQRCode";
// import CallLogs from "../screens/CallLogs";

// const Tab = createBottomTabNavigator();
// const Stack = createNativeStackNavigator();

// // Custom Button for Center Tab (QR Scanner)
// const CustomTabButton = ({ children, onPress }) => (
//   <TouchableOpacity
//     style={{
//       top: -20,
//       justifyContent: "center",
//       alignItems: "center",
//       backgroundColor: "#4CAF50",
//       width: 60,
//       height: 60,
//       borderRadius: 30,
//       elevation: 5,
//     }}
//     onPress={onPress}
//   >
//     {children}
//   </TouchableOpacity>
// );

// const BottomTabNavigator = () => {
//   return (
//     <Tab.Navigator
//       screenOptions={{
//         headerShown: false,
//         tabBarShowLabel: false,
//         tabBarStyle: {
//           position: "absolute",
//           bottom: 20,
//           left: 20,
//           right: 20,
//           elevation: 5,
//           backgroundColor: "#fff",
//           borderRadius: 15,
//           height: 70,
//           shadowColor: "#000",
//           shadowOffset: { width: 0, height: 10 },
//           shadowOpacity: 0.1,
//           shadowRadius: 4,
//         },
//       }}
//     >
//       {/* Home Tab */}
//       <Tab.Screen
//         name="Home"
//         component={Home}
//         options={{
//           tabBarIcon: ({ color }) => <Icon name="home" size={28} color={color || "#333"} />,
//         }}
//       />

//       {/* QR Scanner Tab (Floating Center Button) */}
//       <Tab.Screen
//         name="ScanQRCode"
//         component={ScanQRCode}
//         options={{
//           tabBarIcon: ({ color }) => <Icon name="qrcode" size={28} color="#fff" />,
//           tabBarButton: (props) => <CustomTabButton {...props} />,
//         }}
//       />

//       {/* Call Logs Tab */}
//       <Tab.Screen
//         name="CallLogs"
//         component={CallLogs}
//         options={{
//           tabBarIcon: ({ color }) => <Icon name="phone" size={28} color={color || "#333"} />,
//         }}
//       />
//     </Tab.Navigator>
//   );
// };

// export default BottomTabNavigator;
