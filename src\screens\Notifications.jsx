import React, { useState, useContext } from "react";
import {
  View,
  Text,
  FlatList,
  TouchableOpacity,
  StyleSheet,
  Alert,
  Image,
} from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
import globalStyle from "../styles/global";
import { AuthContext } from "../services/AuthContext";

const Notifications = () => {
  const [notifications, setNotifications] = useState([
    { id: "1", text: "Your QR code was scanned", type: "scan", read: false },
    { id: "2", text: "You have a new reward!", type: "reward", read: false },
    {
      id: "3",
      text: "Parking slot reserved successfully",
      type: "booking",
      read: true,
    },
  ]);
  const { logout, user, setUser, theme, toggleTheme } = useContext(AuthContext);
  const mainPage = globalStyle(theme);
  const notificationIcons = {
    scan: "qrcode-scan",
    reward: "gift",
    booking: "car",
  };

  const markAsRead = (id) => {
    setNotifications((prevNotifications) =>
      prevNotifications.map((notification) =>
        notification.id === id ? { ...notification, read: true } : notification
      )
    );
  };

  const deleteNotification = (id) => {
    setNotifications((prevNotifications) =>
      prevNotifications.filter((notification) => notification.id !== id)
    );
  };

  const markAllAsRead = () => {
    setNotifications((prevNotifications) =>
      prevNotifications.map((notification) => ({ ...notification, read: true }))
    );
    Alert.alert("Success", "All notifications marked as read");
  };

  return (
    <View style={mainPage.container}>
      <View style={styles.header}>
        <Text style={styles.title}>Notifications</Text>
        <TouchableOpacity onPress={markAllAsRead}>
          <Text style={styles.markAllText}>Mark All as Read</Text>
        </TouchableOpacity>
      </View>
      <FlatList
        data={notifications}
        keyExtractor={(item) => item.id}
        renderItem={({ item }) => (
          <TouchableOpacity
            style={[
              styles.notificationCard,
              item.read ? styles.read : styles.unread,
            ]}
            onPress={() => markAsRead(item.id)}
            onLongPress={() => deleteNotification(item.id)}
          >
            <View style={styles.iconContainer}>
              <Icon
                name={notificationIcons[item.type]}
                size={30}
                color="#008080"
              />
            </View>
            <View style={styles.textContainer}>
              <Text style={styles.notificationText}>{item.text}</Text>
              <Text style={styles.timeText}>5 mins ago</Text>
            </View>
            {item.read ? null : <Icon name="circle" size={12} color="red" />}
          </TouchableOpacity>
        )}
      />
    </View>
  );
};

export default Notifications;

const styles = StyleSheet.create({
  header: {
    flexDirection: "row",
    justifyContent: "space-between",
    alignItems: "center",
    marginBottom: 20,
  },
  title: { fontSize: 22, fontWeight: "bold", color: "#333" },
  markAllText: { color: "#008080", fontWeight: "600" },
  notificationCard: {
    flexDirection: "row",
    alignItems: "center",
    padding: 15,
    marginVertical: 5,
    borderRadius: 10,
    backgroundColor: "white",
    elevation: 3,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
  },
  iconContainer: { marginRight: 15 },
  textContainer: { flex: 1 },
  notificationText: { fontSize: 16, fontWeight: "600", color: "#333" },
  timeText: { fontSize: 12, color: "#888", marginTop: 5 },
  unread: { borderLeftWidth: 5, borderLeftColor: "red" },
  read: { borderLeftWidth: 5, borderLeftColor: "#ccc" },
});
