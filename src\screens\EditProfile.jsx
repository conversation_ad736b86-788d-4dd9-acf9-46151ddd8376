import React, { useState, useContext } from "react";
import {
  View,
  Text,
  TextInput,
  StyleSheet,
  TouchableOpacity,
  Image,
  Alert,
} from "react-native";
import { launchImageLibrary } from "react-native-image-picker";
import { AuthContext } from "../services/AuthContext";
import AsyncStorage from "@react-native-async-storage/async-storage";
import Icon from "react-native-vector-icons/FontAwesome";
import btn from "../styles/buttons";
import globalStyle from "../styles/global";

const EditProfile = ({ navigation }) => {
  const { user, setUser, theme } = useContext(AuthContext);
  const [name, setName] = useState(user?.name || "");
  const [email, setEmail] = useState(user?.email || "");
  const [profilePic, setProfilePic] = useState(
    user?.profilePic || "https://via.placeholder.com/100"
  );

  const mainPage = globalStyle(theme);
  const mainBtn = btn(theme);

  const pickImage = async () => {
    const options = {
      mediaType: "photo",
      maxWidth: 500,
      maxHeight: 500,
      quality: 1,
      includeBase64: false,
    };

    launchImageLibrary(options, (response) => {
      if (response.didCancel) {
        console.log("User cancelled image picker");
      } else if (response.errorCode) {
        console.log("Image Picker Error: ", response.errorMessage);
      } else if (response.assets && response.assets.length > 0) {
        const newImageUri = response.assets[0].uri;
        setProfilePic(newImageUri);
      }
    });
  };

  const handleSave = async () => {
    if (!name || !email) {
      Alert.alert("Missing Fields", "Please fill in all fields before saving.");
      return;
    }

    const updatedUser = { ...user, name, email, profilePic };
    setUser(updatedUser);
    await AsyncStorage.setItem("user", JSON.stringify(updatedUser));
    Alert.alert("Success", "Profile updated successfully!");
    navigation.goBack();
  };

  return (
    <View style={mainPage.container}>
      <View style={styles.page}>
        <TouchableOpacity onPress={pickImage} style={styles.imageWrapper}>
          <Image source={{ uri: profilePic }} style={styles.avatar} />
          <View style={styles.cameraIcon}>
            <Icon name="camera" size={18} color="white" />
          </View>
        </TouchableOpacity>
        <Text style={styles.changePhotoText}>Tap to Change Photo</Text>

        <TextInput
          style={styles.input}
          value={name}
          onChangeText={setName}
          placeholder="Full Name"
          placeholderTextColor="#999"
        />
        <TextInput
          style={styles.input}
          value={email}
          onChangeText={setEmail}
          placeholder="Email"
          keyboardType="email-address"
          placeholderTextColor="#999"
        />

        <TouchableOpacity
          style={mainBtn.appBtn}
          onPress={handleSave}
          activeOpacity={0.8}
        >
          <Text style={mainBtn.text}>Save Profile</Text>
        </TouchableOpacity>
      </View>
    </View>
  );
};

export default EditProfile;

const styles = StyleSheet.create({
  page: {
    display: "flex",
    alignItems: "center",
    height: "100%",
    paddingTop: 30,
    padding: 10,
    width: "100%",
  },
  imageWrapper: {
    position: "relative",
    alignItems: "center",
    justifyContent: "center",
    marginBottom: 15,
  },
  avatar: {
    width: 120,
    height: 120,
    borderRadius: 60,
    borderWidth: 3,
    borderColor: "#4e54c8",
    backgroundColor: "#ddd",
  },
  cameraIcon: {
    position: "absolute",
    bottom: 5,
    right: 10,
    backgroundColor: "#4e54c8",
    padding: 8,
    borderRadius: 20,
    elevation: 5,
  },
  changePhotoText: {
    color: "#4e54c8",
    fontSize: 14,
    marginBottom: 20,
  },
  input: {
    width: "100%",
    padding: 14,
    marginBottom: 12,
    borderWidth: 1,
    borderColor: "#ccc",
    borderRadius: 12,
    backgroundColor: "#fff",
    fontSize: 16,
    elevation: 2,
  },
});
