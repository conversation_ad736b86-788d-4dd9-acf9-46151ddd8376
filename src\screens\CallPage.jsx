import React, { useContext, useState, useEffect } from "react";
import { 
  View, 
  Text, 
  TouchableOpacity, 
  StyleSheet, 
  Linking, 
  SafeAreaView, 
  Alert,
  Animated,
  Dimensions,
  Platform,
  ScrollView,
  StatusBar
} from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
import { AuthContext } from "../services/AuthContext";
import AlertComponent from "./AlertFeature";
import BackButton from "../components/BackButton";

const { width, height } = Dimensions.get('window');

const CallPage = ({ route, navigation }) => {
  const { theme } = useContext(AuthContext);
  const { ownerName, phoneNumber, userId, vehicleInfo } = route.params;
  const [alertModalVisible, setAlertModalVisible] = useState(false);
  
  // Animation values
  const fadeAnim = new Animated.Value(0);
  const slideAnim = new Animated.Value(50);
  
  useEffect(() => {
    // Animate components when screen loads
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 500,
        useNativeDriver: true
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 500,
        useNativeDriver: true
      })
    ]).start();
  }, []);

  const handleCall = () => {
    if (phoneNumber) {
      Linking.openURL(`tel:${phoneNumber}`);
    } else {
      Alert.alert("Error", "No phone number available");
    }
  };

  return (
    <SafeAreaView style={[styles.container, { backgroundColor: theme.backgroundColor }]}>
      <StatusBar backgroundColor={theme.backgroundColor} barStyle={theme.isLight ? "dark-content" : "light-content"} />
      
      <View style={styles.header}>
        <BackButton onPress={() => navigation.goBack()} />
        <Text style={[styles.headerTitle, { color: theme.textColor }]}>Contact Vehicle Owner</Text>
      </View>

      <ScrollView 
        contentContainerStyle={styles.scrollContent}
        showsVerticalScrollIndicator={false}
      >
        <Animated.View 
          style={[
            styles.ownerInfoContainer, 
            { 
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          <View style={[styles.avatarContainer, { backgroundColor: theme.primaryColor }]}>
            <Text style={styles.avatarText}>{ownerName.charAt(0).toUpperCase()}</Text>
          </View>
          <Text style={[styles.ownerName, { color: theme.textColor }]}>{ownerName}</Text>
          {vehicleInfo && (
            <View style={styles.vehicleInfoContainer}>
              <Icon name="car" size={20} color={theme.textColor} style={styles.vehicleIcon} />
              <Text style={[styles.vehicleInfo, { color: theme.textColor }]}>{vehicleInfo}</Text>
            </View>
          )}
        </Animated.View>

        <Animated.View 
          style={[
            styles.actionContainer,
            {
              opacity: fadeAnim,
              transform: [{ translateY: slideAnim }]
            }
          ]}
        >
          <Text style={[styles.actionTitle, { color: theme.textColor }]}>Choose an action:</Text>
          
          {phoneNumber && (
            <TouchableOpacity 
              style={[styles.callButton, { backgroundColor: theme.primaryColor }]} 
              onPress={handleCall}
              activeOpacity={0.8}
            >
              <View style={styles.buttonIconContainer}>
                <Icon name="phone" size={28} color="#FFF" />
              </View>
              <View style={styles.buttonTextContainer}>
                <Text style={styles.callText}>Call Owner</Text>
                <Text style={styles.callSubtext}>Direct phone call to vehicle owner</Text>
              </View>
            </TouchableOpacity>
          )}

          <TouchableOpacity
            style={styles.alertButton}
            onPress={() => setAlertModalVisible(true)}
            activeOpacity={0.8}
          >
            <View style={styles.buttonIconContainer}>
              <Icon name="bell-ring" size={28} color="#FFF" />
            </View>
            <View style={styles.buttonTextContainer}>
              <Text style={styles.alertText}>Send Alert</Text>
              <Text style={styles.alertSubtext}>Notify owner to move their vehicle</Text>
            </View>
          </TouchableOpacity>
        </Animated.View>
      </ScrollView>

      <AlertComponent
        userId={userId}
        visible={alertModalVisible}
        onClose={() => setAlertModalVisible(false)}
      />
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  scrollContent: {
    flexGrow: 1,
    paddingHorizontal: 20,
    paddingBottom: 30,
  },
  header: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingTop: Platform.OS === 'android' ? 15 : 10,
    paddingBottom: 15,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(0,0,0,0.05)',
  },
  headerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 15,
  },
  ownerInfoContainer: {
    alignItems: 'center',
    marginTop: height * 0.05,
    marginBottom: height * 0.05,
  },
  avatarContainer: {
    width: width * 0.3,
    height: width * 0.3,
    borderRadius: width * 0.15,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 4.65,
    elevation: 8,
  },
  avatarText: {
    fontSize: width * 0.15,
    fontWeight: 'bold',
    color: 'white',
  },
  ownerName: {
    fontSize: 28,
    fontWeight: 'bold',
    marginBottom: 10,
    textAlign: 'center',
  },
  vehicleInfoContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(0,0,0,0.05)',
    paddingVertical: 8,
    paddingHorizontal: 15,
    borderRadius: 20,
  },
  vehicleIcon: {
    marginRight: 8,
  },
  vehicleInfo: {
    fontSize: 16,
    fontWeight: '500',
  },
  actionContainer: {
    width: '100%',
    paddingHorizontal: 5,
  },
  actionTitle: {
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 15,
    marginLeft: 5,
  },
  callButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderRadius: 15,
    marginBottom: 15,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  alertButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 20,
    borderRadius: 15,
    backgroundColor: "#FF9800",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  buttonIconContainer: {
    width: 50,
    height: 50,
    borderRadius: 25,
    backgroundColor: 'rgba(255,255,255,0.2)',
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 15,
  },
  buttonTextContainer: {
    flex: 1,
  },
  callText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  callSubtext: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
  },
  alertText: {
    fontSize: 18,
    fontWeight: 'bold',
    color: 'white',
    marginBottom: 4,
  },
  alertSubtext: {
    fontSize: 14,
    color: 'rgba(255,255,255,0.8)',
  },
});

export default CallPage;
