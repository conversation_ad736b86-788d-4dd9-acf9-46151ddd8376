{"name": "QRParkingAppBare", "version": "0.0.1", "private": true, "scripts": {"android": "react-native run-android", "ios": "react-native run-ios", "lint": "eslint .", "start": "react-native start", "test": "jest"}, "dependencies": {"@notifee/react-native": "^9.1.8", "@react-native-async-storage/async-storage": "^2.1.2", "@react-native-community/slider": "^4.5.6", "@react-navigation/bottom-tabs": "^7.3.8", "@react-navigation/native": "^7.1.4", "@react-navigation/native-stack": "^7.3.8", "axios": "^1.8.4", "react": "18.3.1", "react-native": "0.76.0", "react-native-animatable": "^1.4.0", "react-native-gesture-handler": "^2.25.0", "react-native-google-mobile-ads": "^15.2.0", "react-native-haptic-feedback": "^2.3.3", "react-native-image-picker": "^8.2.0", "react-native-linear-gradient": "^2.8.3", "react-native-paper": "^5.13.1", "react-native-permissions": "^5.3.0", "react-native-qrcode-svg": "^6.3.15", "react-native-reanimated": "^3.17.2", "react-native-safe-area-context": "^5.3.0", "react-native-screens": "^4.10.0", "react-native-svg": "^15.11.2", "react-native-vector-icons": "^10.2.0", "react-native-vision-camera": "^4.6.4"}, "devDependencies": {"@babel/core": "^7.25.2", "@babel/preset-env": "^7.25.3", "@babel/runtime": "^7.25.0", "@react-native-community/cli": "15.0.0-alpha.2", "@react-native-community/cli-platform-android": "15.0.0-alpha.2", "@react-native-community/cli-platform-ios": "15.0.0-alpha.2", "@react-native/babel-preset": "0.76.0", "@react-native/eslint-config": "0.76.0", "@react-native/metro-config": "0.76.0", "@react-native/typescript-config": "0.76.0", "@types/react": "^18.2.6", "@types/react-test-renderer": "^18.0.0", "babel-jest": "^29.6.3", "eslint": "^8.19.0", "jest": "^29.6.3", "prettier": "2.8.8", "react-test-renderer": "18.3.1", "typescript": "5.0.4"}, "engines": {"node": ">=18"}}