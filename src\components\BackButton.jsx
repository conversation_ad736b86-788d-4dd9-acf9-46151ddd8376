import React, { useContext } from "react";
import { TouchableOpacity, StyleSheet, View, Text } from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
import { useNavigation } from "@react-navigation/native";
import { AuthContext } from "../services/AuthContext";

const BackButton = ({ 
  onPress, 
  title = "Back",
  showTitle = true,
  style,
  iconSize = 24,
  iconName = "arrow-left"
}) => {
  const navigation = useNavigation();
  const { theme } = useContext(AuthContext);

  const handlePress = () => {
    if (onPress) {
      // If custom onPress is provided, use it
      onPress();
    } else {
      // Otherwise use navigation to go back
      navigation.goBack();
    }
  };

  return (
    <View style={[styles.container, style]}>
      <TouchableOpacity
        onPress={handlePress}
        activeOpacity={0.7}
        style={[
          styles.button, 
          { backgroundColor: theme.cardColor }
        ]}
      >
        <Icon name={iconName} size={iconSize} color={theme.primaryColor} />
        {showTitle && (
          <Text style={[styles.text, { color: theme.textColor }]}>
            {title}
          </Text>
        )}
      </TouchableOpacity>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    marginTop: 40,
    marginLeft: 16,
    alignSelf: "flex-start",
  },
  button: {
    flexDirection: "row",
    alignItems: "center",
    padding: 12,
    borderRadius: 12,
  },
  text: {
    fontSize: 16,
    fontWeight: "500",
    marginLeft: 8,
  }
});

export default BackButton;
