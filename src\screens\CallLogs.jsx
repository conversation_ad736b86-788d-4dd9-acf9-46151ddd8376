import React, { useContext } from "react";
import { View, Text, StyleSheet, FlatList, TouchableOpacity } from "react-native";
import BackButton from "../components/BackButton";
import globalStyle from "../styles/global";
import { AuthContext } from "../services/AuthContext";
import { useNavigation } from "@react-navigation/native";

const mockCallLogs = [
  { id: 1, name: "<PERSON>", time: "10:30 AM", date: "2025-01-24" },
  { id: 2, name: "<PERSON>", time: "2:15 PM", date: "2025-01-23" }
];

const CallLogs = () => {
  const { theme } = useContext(AuthContext);
  const navigation = useNavigation();
  const mainPage = globalStyle(theme);
  
  const renderCallLog = ({ item }) => (
    <TouchableOpacity 
      style={[styles.logCard, {backgroundColor: theme.cardColor}]}
      activeOpacity={0.7}
    >
      <Text style={[styles.name, {color: theme.textColor}]}>{item.name}</Text>
      <Text style={styles.details}>{`${item.time}, ${item.date}`}</Text>
    </TouchableOpacity>
  );

  // Add a custom handler for the back button
  const handleBackPress = () => {
    // Navigate to Home or another specific screen instead of using goBack
    navigation.navigate("MainTabs");
  };

  return (
    <View style={mainPage.container}>
      <BackButton onPress={handleBackPress} />
      <Text style={[styles.header, {color: theme.textColor}]}>Call Logs</Text>
      <FlatList
        data={mockCallLogs}
        keyExtractor={(item) => item.id.toString()}
        renderItem={renderCallLog}
        contentContainerStyle={styles.listContainer}
        showsVerticalScrollIndicator={false}
      />
    </View>
  );
};

export default CallLogs;

const styles = StyleSheet.create({
  header: {
    fontSize: 24,
    fontWeight: "bold",
    marginVertical: 20,
    textAlign: "center",
    width: '100%'
  },
  listContainer: {
    width: '100%',
    paddingHorizontal: 10,
    paddingBottom: 20
  },
  logCard: {
    padding: 15,
    marginBottom: 12,
    borderRadius: 10,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    elevation: 3,
    width: '100%'
  },
  name: {
    fontSize: 18,
    fontWeight: "bold",
    marginBottom: 5
  },
  details: {
    fontSize: 14,
    color: "#666",
  },
});
