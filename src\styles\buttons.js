import { StyleSheet } from "react-native";

const btn = (theme) => StyleSheet.create({
  login: {
    backgroundColor: theme.primaryColor,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: "center",
    minWidth: "80%",
  },

  appBtn: {
    backgroundColor: theme.primaryColor,
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: "center",
    minWidth: "100%",
    display:"flex",
    flexDirection:"row",
    justifyContent:"center",
  },
  
  reg_qr: {
    backgroundColor: "#4CAF50",
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: "center",
    minWidth: "80%",
  },

  reg_man:{
    backgroundColor: "#2196F3",
    paddingVertical: 15,
    borderRadius: 10,
    alignItems: "center",
    minWidth: "80%",
  },

  feature: {
    flexDirection: "row",
    alignItems: "center",
    backgroundColor: "#FFF",
    borderRadius: 10,
    padding: 20,
    marginBottom: 20,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
  },

  round: {
    backgroundColor: "#4CAF50", // Matches theme color
    width: 45,
    height: 45,
    borderRadius: 25,
    justifyContent: "center",
    alignItems: "center",
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.2,
    shadowRadius: 4,
    elevation: 5, // Adds shadow for Android
  },

  featureText: {
    fontSize: 18,
    fontWeight: "600",
    marginLeft: 20,
    color: "#333",
  },

  text:{
    color: theme.btnText,
    fontSize: 16,
    fontWeight: "600",
  }
});

export default btn;
