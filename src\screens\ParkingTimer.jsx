import React, { useState, useEffect, useRef, useContext } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, Animated, TextInput, Alert, ScrollView, InteractionManager } from 'react-native';
import Icon from 'react-native-vector-icons/MaterialCommunityIcons';
import AsyncStorage from '@react-native-async-storage/async-storage';
import { LanguageContext } from '../services/LanguageContext';
import { AuthContext } from '../services/AuthContext';

const ParkingTimer = ({ navigation }) => {
  const { theme } = useContext(AuthContext);
  const [isParked, setIsParked] = useState(false);
  const [parkingTime, setParkingTime] = useState(0);
  const [parkingLocation, setParkingLocation] = useState('');
  const [estimatedCost, setEstimatedCost] = useState(0);
  const [hourlyRate, setHourlyRate] = useState(30); // Default rate in ₹ (30 rupees per hour)
  const [notes, setNotes] = useState('');
  const [parkingHistory, setParkingHistory] = useState([]);
  const [locationOptions, setLocationOptions] = useState([
    'Mall Parking',
    'Metro Station Parking',
    'Airport Parking',
    'Street Parking',
    'Office Building Parking'
  ]);
  const [showLocationPicker, setShowLocationPicker] = useState(false);
  const timerRef = useRef(null);
  const pulseAnim = useRef(new Animated.Value(1)).current;
  
  // Load saved data when component mounts
  useEffect(() => {
    const loadSavedData = async () => {
      try {
        // Load active parking session if exists
        const savedSession = await AsyncStorage.getItem('activeParkingSession');
        if (savedSession) {
          const sessionData = JSON.parse(savedSession);
          setIsParked(true);
          setParkingLocation(sessionData.location);
          setParkingTime(Math.floor((Date.now() - sessionData.startTime) / 1000));
          setHourlyRate(sessionData.hourlyRate);
          setNotes(sessionData.notes || '');
        }
        
        // Load parking history
        const history = await AsyncStorage.getItem('parkingHistory');
        if (history) {
          setParkingHistory(JSON.parse(history));
        }
        
        // Load saved locations
        const savedLocations = await AsyncStorage.getItem('parkingLocations');
        if (savedLocations) {
          setLocationOptions(JSON.parse(savedLocations));
        }
      } catch (error) {
        console.error('Error loading saved parking data:', error);
      }
    };
    
    // Use InteractionManager to ensure UI is responsive during load
    InteractionManager.runAfterInteractions(() => {
      loadSavedData();
    });
  }, []);
  
  useEffect(() => {
    if (isParked) {
      timerRef.current = setInterval(() => {
        setParkingTime(prev => prev + 1);
        // Calculate cost based on hourly rate
        setEstimatedCost(((parkingTime + 1) / 3600) * hourlyRate);
      }, 1000);
      
      // Start pulse animation
      Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.05,
            duration: 1000,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1000,
            useNativeDriver: true,
          }),
        ])
      ).start();
    } else {
      clearInterval(timerRef.current);
      pulseAnim.setValue(1);
    }
    
    return () => clearInterval(timerRef.current);
  }, [isParked, parkingTime, hourlyRate]);
  
  const formatTime = (seconds) => {
    const hrs = Math.floor(seconds / 3600);
    const mins = Math.floor((seconds % 3600) / 60);
    const secs = seconds % 60;
    return `${hrs.toString().padStart(2, '0')}:${mins.toString().padStart(2, '0')}:${secs.toString().padStart(2, '0')}`;
  };
  
  const startParking = async () => {
    if (!parkingLocation) {
      Alert.alert('Location Required', 'Please select or enter a parking location');
      return;
    }
    
    const startTime = Date.now();
    setParkingTime(0);
    setEstimatedCost(0);
    setIsParked(true);
    
    // Save active session to AsyncStorage
    try {
      await AsyncStorage.setItem('activeParkingSession', JSON.stringify({
        location: parkingLocation,
        startTime,
        hourlyRate,
        notes
      }));
      
      // Add location to saved locations if it's new
      if (!locationOptions.includes(parkingLocation)) {
        const newLocations = [parkingLocation, ...locationOptions.slice(0, 9)];
        setLocationOptions(newLocations);
        await AsyncStorage.setItem('parkingLocations', JSON.stringify(newLocations));
      }
    } catch (error) {
      console.error('Error saving parking session:', error);
    }
  };
  
  const stopParking = async () => {
    // Calculate final values
    const duration = parkingTime;
    const finalCost = estimatedCost;
    
    // Create history entry
    const historyEntry = {
      id: Date.now().toString(),
      location: parkingLocation,
      date: new Date().toISOString(),
      duration,
      cost: finalCost,
      notes
    };
    
    // Update history state and save to storage
    const updatedHistory = [historyEntry, ...parkingHistory];
    setParkingHistory(updatedHistory);
    
    try {
      await AsyncStorage.setItem('parkingHistory', JSON.stringify(updatedHistory));
      await AsyncStorage.removeItem('activeParkingSession');
    } catch (error) {
      console.error('Error saving parking history:', error);
    }
    
    // Reset current session
    setIsParked(false);
    setNotes('');
    
    // Show summary alert
    Alert.alert(
      'Parking Session Ended',
      `Location: ${parkingLocation}\nDuration: ${formatTime(duration)}\nTotal Cost: ₹${finalCost.toFixed(2)}`,
      [
        { text: 'OK' },
        { 
          text: 'View History', 
          onPress: () => navigation.navigate('ParkingHistory', { refreshHistory: true }) 
        }
      ]
    );
  };
  
  const addCustomLocation = (location) => {
    setParkingLocation(location);
    setShowLocationPicker(false);
  };
  
  return (
    <ScrollView 
      style={[styles.container, { backgroundColor: theme.backgroundColor }]} 
      contentContainerStyle={styles.contentContainer}
    >
      <Animated.View 
        style={[
          styles.timerCard, 
          { backgroundColor: theme.cardColor },
          isParked && styles.activeTimerCard,
          { transform: [{ scale: pulseAnim }] }
        ]}
      >
        <View style={styles.timerHeader}>
          <Icon name="car-clock" size={30} color={isParked ? "#fff" : theme.textColor} />
          <Text style={[
            styles.timerTitle, 
            { color: isParked ? "#fff" : theme.textColor }
          ]}>
            {isParked ? 'Currently Parked' : 'Parking Timer'}
          </Text>
        </View>
        
        {isParked ? (
          <View style={styles.parkingDetails}>
            <Text style={styles.locationText}>{parkingLocation}</Text>
            <Text style={styles.timeText}>{formatTime(parkingTime)}</Text>
            <Text style={styles.costText}>Est. Cost: ₹{estimatedCost.toFixed(2)}</Text>
            
            {notes ? (
              <View style={styles.notesContainer}>
                <Text style={styles.notesLabel}>Notes:</Text>
                <Text style={styles.notesText}>{notes}</Text>
              </View>
            ) : null}
          </View>
        ) : (
          <View style={styles.setupContainer}>
            <TouchableOpacity 
              style={styles.locationSelector}
              onPress={() => setShowLocationPicker(!showLocationPicker)}
            >
              <Text style={[styles.locationLabel, { color: theme.textColor }]}>Parking Location:</Text>
              <View style={styles.locationInputContainer}>
                <TextInput
                  style={[styles.locationInput, { color: theme.textColor }]}
                  value={parkingLocation}
                  onChangeText={setParkingLocation}
                  placeholder="Select or enter location"
                  placeholderTextColor="#999"
                />
                <Icon name={showLocationPicker ? "chevron-up" : "chevron-down"} size={24} color={theme.textColor} />
              </View>
            </TouchableOpacity>
            
            {showLocationPicker && (
              <View style={[styles.locationOptions, { backgroundColor: theme.cardColor }]}>
                {locationOptions.map((location, index) => (
                  <TouchableOpacity 
                    key={index}
                    style={styles.locationOption}
                    onPress={() => {
                      setParkingLocation(location);
                      setShowLocationPicker(false);
                    }}
                  >
                    <Text style={[styles.locationOptionText, { color: theme.textColor }]}>{location}</Text>
                  </TouchableOpacity>
                ))}
              </View>
            )}
            
            <View style={styles.rateContainer}>
              <Text style={[styles.rateLabel, { color: theme.textColor }]}>Hourly Rate (₹):</Text>
              <TextInput
                style={[styles.rateInput, { color: theme.textColor }]}
                value={hourlyRate.toString()}
                onChangeText={(text) => {
                  const rate = parseFloat(text) || 0;
                  setHourlyRate(rate);
                }}
                keyboardType="numeric"
              />
            </View>
            
            <View style={styles.notesInputContainer}>
              <Text style={[styles.notesInputLabel, { color: theme.textColor }]}>Notes:</Text>
              <TextInput
                style={[styles.notesInput, { color: theme.textColor }]}
                value={notes}
                onChangeText={setNotes}
                placeholder="Add parking spot number, level, etc."
                placeholderTextColor="#999"
                multiline
              />
            </View>
          </View>
        )}
        
        <TouchableOpacity 
          style={[styles.timerButton, isParked ? styles.stopButton : styles.startButton]} 
          onPress={isParked ? stopParking : startParking}
        >
          <Text style={styles.buttonText}>
            {isParked ? 'End Parking' : 'Start Parking Timer'}
          </Text>
        </TouchableOpacity>
      </Animated.View>
      
      {isParked && (
        <View style={[styles.reminderCard, { backgroundColor: theme.cardColor }]}>
          <Text style={[styles.reminderTitle, { color: theme.textColor }]}>Parking Reminders</Text>
          <Text style={[styles.reminderText, { color: theme.textColor }]}>
            • Remember to check your parking time limit
          </Text>
          <Text style={[styles.reminderText, { color: theme.textColor }]}>
            • Don't forget to collect your parking receipt
          </Text>
          <Text style={[styles.reminderText, { color: theme.textColor }]}>
            • Take a photo of your parking spot to remember location
          </Text>
        </View>
      )}
      
      {!isParked && parkingHistory.length > 0 && (
        <View style={[styles.recentHistoryContainer, { backgroundColor: theme.cardColor }]}>
          <View style={styles.recentHistoryHeader}>
            <Text style={[styles.recentHistoryTitle, { color: theme.textColor }]}>Recent Parking</Text>
            <TouchableOpacity 
              onPress={() => navigation.navigate('ParkingHistory')}
              style={styles.viewAllButton}
            >
              <Text style={[styles.viewAllText, { color: theme.primaryColor }]}>View All</Text>
            </TouchableOpacity>
          </View>
          
          {parkingHistory.slice(0, 2).map((item) => (
            <View key={item.id} style={styles.historyItem}>
              <View style={styles.historyItemLeft}>
                <Icon name="map-marker" size={24} color={theme.primaryColor} style={styles.historyIcon} />
                <View>
                  <Text style={[styles.historyLocationText, { color: theme.textColor }]}>{item.location}</Text>
                  <Text style={styles.historyDateText}>
                    {new Date(item.date).toLocaleDateString()}
                  </Text>
                </View>
              </View>
              <View style={styles.historyItemRight}>
                <Text style={[styles.historyDurationText, { color: theme.textColor }]}>{formatTime(item.duration)}</Text>
                <Text style={styles.historyCostText}>₹{item.cost.toFixed(2)}</Text>
              </View>
            </View>
          ))}
        </View>
      )}
    </ScrollView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  contentContainer: {
    padding: 20,
  },
  timerCard: {
    borderRadius: 15,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  activeTimerCard: {
    backgroundColor: '#4361ee',
  },
  timerHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 20,
  },
  timerTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    marginLeft: 10,
  },
  activeText: {
    color: '#fff',
  },
  parkingDetails: {
    marginBottom: 20,
  },
  locationText: {
    fontSize: 16,
    color: '#fff',
    marginBottom: 10,
  },
  timeText: {
    fontSize: 36,
    fontWeight: 'bold',
    color: '#fff',
    marginBottom: 10,
    textAlign: 'center',
  },
  costText: {
    fontSize: 18,
    color: '#fff',
    textAlign: 'center',
    marginBottom: 10,
  },
  notesContainer: {
    backgroundColor: 'rgba(255,255,255,0.2)',
    padding: 10,
    borderRadius: 8,
    marginTop: 10,
  },
  notesLabel: {
    color: '#fff',
    fontWeight: 'bold',
    marginBottom: 5,
  },
  notesText: {
    color: '#fff',
  },
  setupContainer: {
    marginBottom: 20,
  },
  locationSelector: {
    marginBottom: 15,
  },
  locationLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  locationInputContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 10,
  },
  locationInput: {
    flex: 1,
    paddingVertical: 12,
    fontSize: 16,
  },
  locationOptions: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    marginBottom: 15,
    maxHeight: 200,
  },
  locationOption: {
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  locationOptionText: {
    fontSize: 16,
  },
  rateContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 15,
  },
  rateLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    width: 120,
  },
  rateInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 8,
    fontSize: 16,
  },
  notesInputContainer: {
    marginBottom: 15,
  },
  notesInputLabel: {
    fontSize: 16,
    fontWeight: 'bold',
    marginBottom: 8,
  },
  notesInput: {
    borderWidth: 1,
    borderColor: '#ddd',
    borderRadius: 8,
    paddingHorizontal: 10,
    paddingVertical: 8,
    fontSize: 16,
    minHeight: 80,
    textAlignVertical: 'top',
  },
  timerButton: {
    padding: 15,
    borderRadius: 10,
    alignItems: 'center',
  },
  startButton: {
    backgroundColor: '#4CAF50',
  },
  stopButton: {
    backgroundColor: '#f44336',
  },
  buttonText: {
    color: '#fff',
    fontSize: 16,
    fontWeight: 'bold',
  },
  reminderCard: {
    borderRadius: 15,
    padding: 20,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  reminderTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    marginBottom: 15,
  },
  reminderText: {
    fontSize: 14,
    marginBottom: 10,
  },
  recentHistoryContainer: {
    borderRadius: 15,
    padding: 20,
    marginTop: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 3,
  },
  recentHistoryHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  recentHistoryTitle: {
    fontSize: 18,
    fontWeight: 'bold',
  },
  viewAllButton: {
    padding: 5,
  },
  viewAllText: {
    fontWeight: 'bold',
  },
  historyItem: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 12,
    borderBottomWidth: 1,
    borderBottomColor: '#eee',
  },
  historyItemLeft: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  historyIcon: {
    marginRight: 10,
  },
  historyLocationText: {
    fontSize: 16,
    fontWeight: '500',
  },
  historyDateText: {
    fontSize: 14,
    color: '#666',
  },
  historyItemRight: {
    alignItems: 'flex-end',
  },
  historyDurationText: {
    fontSize: 16,
    fontWeight: '500',
  },
  historyCostText: {
    fontSize: 14,
    color: '#4CAF50',
    fontWeight: 'bold',
  },
});

export default ParkingTimer;

