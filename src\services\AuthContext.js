import React, { createContext, useState, useEffect } from "react";
import AsyncStorage from "@react-native-async-storage/async-storage";
import { POST } from "./api";
import { URLS } from "./url";

export const AuthContext = createContext();

export const AuthProvider = ({ children }) => {
  const themeData = {
    light: {
      backgroundColor: "#F8F9FA",
      textColor: "black",
      cardColor: "#fff",
      primaryColor: "#4e54c8",
      btnText:"white",
      isLight:true
    },
    dark: {
      backgroundColor: "#000",
      textColor: "white", // Text color is white for dark mode
      cardColor: "#222", // Darker card
      // primaryColor: "#1E90FF",
      btnText:"white",
      primaryColor: "#4e54c8",
      isLight:false

    },
  };
  
  
  const [isAuthenticated, setIsAuthenticated] = useState(null);
  const [user, setUser] = useState({});
  const [themeMode, setThemeMode] = useState("light");
  const [theme, setTheme] = useState(themeData[themeMode]);

  useEffect(() => {
    const checkAuthStatus = async () => {
      try {
        const storedUser = await AsyncStorage.getItem("user");
        if (storedUser) {
          const parsedUser = JSON.parse(storedUser);
          setUser(parsedUser);
          setIsAuthenticated(parsedUser.isAuthenticated);
        } else {
          setUser({});
          setIsAuthenticated(false);
        }
      } catch (error) {
        console.log("Error checking auth status:", error);
        setUser({});
        setIsAuthenticated(false);
      }
    };

    checkAuthStatus();
  }, []);

  const login = async (userData) => {
    // Store the user data with authentication flag
    const userWithAuth = {
      ...userData,
      isAuthenticated: true,
    };
    await AsyncStorage.setItem("user", JSON.stringify(userWithAuth));
    setUser(userWithAuth);
    setIsAuthenticated(true);
  };

  const logout = async () => {
    await AsyncStorage.removeItem("user");
    setUser({});
    setIsAuthenticated(false);
  };

  const toggleTheme = () => {
    setThemeMode((prevMode) => {
      const newMode = prevMode === "light" ? "dark" : "light";
      setTheme(themeData[newMode]); // Apply new theme immediately
      return newMode;
    });
  };
  
  

  useEffect(()=>{
    setTheme(themeData[themeMode])
  },[themeMode])

    console.log("theme", themeMode, theme)

  return (
    <AuthContext.Provider
      value={{
        user,
        setUser,
        isAuthenticated,
        login,
        logout,
        theme: theme,
        toggleTheme,
      }}
    >
      {children}
    </AuthContext.Provider>
  );
};

export default AuthProvider;
