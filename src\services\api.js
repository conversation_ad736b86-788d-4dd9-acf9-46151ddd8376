// import AsyncStorage from "@react-native-async-storage/async-storage";
// import axios from "axios";
// import { URLS } from "./url";

// axios.interceptors.response.use(response => {
//   console.log("Response:", response.status);
//   return response;
// });

// const getAuthToken = async () => {
//   const user = await AsyncStorage.getItem("user");
//   return user ? JSON.parse(user) : null;
// };

// const refreshAuthToken = async () => {
//   try {
//     const token = await getAuthToken();
//     if (!token?.refresh_token) throw new Error("No refresh token available");

//     const data_RF = await axios.post(URLS.refreshToken, { refresh: token.refresh_token });
//     const newData = { 
//       ...token, 
//       access_token: data_RF.data.access,  
//       refresh_token: data_RF.data.refresh 
//     };

//     await AsyncStorage.setItem("user", JSON.stringify(newData));
//     return newData.access_token;
//   } catch (error) {
//     await AsyncStorage.removeItem("user"); 
//     throw new Error("Session expired, please log in again");
//   }
// };


// export const GET = async (urls, params = {}) => {
//   try {
//     let token = await getAuthToken();
//     const response = await axios.get(urls, {
//       params,
//       headers: {
//         Authorization: token?.access_token ? `Bearer ${token.access_token}` : "",
//       },
//     });

//     return response.data;
//   } catch (error) {
//     if (error.response?.status === 401) {
//       try {
//         await refreshAuthToken();
//         return GET(urls, params); 
//       } catch (refreshError) {
//         return { success: false, message: "Session expired, please log in again" };
//       }
//     }

//     console.error(`Error in GET ${urls}:`, error.response?.data || error.message);
//     return { success: false, message: error.response?.data?.message || "Request failed" };
//   }
// };


// export const POST = async (urls, data = {}) => {
//   try {
//     let token = await getAuthToken();
//     const response = await axios.post(urls, data, {
//       headers: {
//         Authorization: token?.access_token ? `Bearer ${token.access_token}` : "",
//         "Content-Type": "application/json",
//       },
//     });

//     return response.data;
//   } catch (error) {
//     if (error.response?.status === 401) {
//       try {
//         await refreshAuthToken();
//         return POST(urls, data); 
//       } catch (refreshError) {
//         return { success: false, message: "Session expired, please log in again" };
//       }
//     }
//     console.error(`Error in POST ${urls}:`, error.response?.data || error.message);
//     return { success: false, message: error.response?.data?.message || "Request failed" };
//   }
// };
