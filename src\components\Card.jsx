import React, { useContext } from "react";
import { StyleSheet, Text, TouchableOpacity } from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
import { AuthContext } from "../services/AuthContext"; // Import AuthContext

export default function Card({ onPress, name, text, color = "#4CAF50", count, countTitle }) {
  const { theme } = useContext(AuthContext); // Get theme from context

  return (
    <TouchableOpacity style={[styles.featureCard, { backgroundColor: theme.cardColor }]} onPress={onPress}>
      {name && <Icon name={name} size={40} color={color} />}
      {count && <Text style={[styles.count, { color: theme.textColor }]}>{count}</Text>}
      {countTitle && <Text style={[styles.countTitle, { color: theme.textColor }]}>{countTitle}</Text>}
      {text && <Text style={[styles.text, { color: theme.textColor }]}>{text}</Text>}
    </TouchableOpacity>
  );
}

const styles = StyleSheet.create({
  featureCard: {
    padding: 20,
    borderRadius: 12,
    alignItems: "center",
    width: 100,
    marginBottom: 15,
    elevation: 4,
  },
  count: { fontSize: 18, fontWeight: "bold" },
  countTitle: { fontSize: 12 },
});
