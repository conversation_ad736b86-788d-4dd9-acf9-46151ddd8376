import React, { useState, useContext, useRef, useEffect } from "react";
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  TextInput,
  Animated,
  ScrollView,
  Keyboard,
  Vibration,
  Dimensions,
  KeyboardAvoidingView,
  Platform,
  SafeAreaView,
} from "react-native";
import Icon from "react-native-vector-icons/MaterialCommunityIcons";
import { AuthContext } from "../services/AuthContext";

const { height, width } = Dimensions.get("window");

const AlertComponent = ({ userId, visible, onClose }) => {
  const { theme } = useContext(AuthContext);
  const [selectedTime, setSelectedTime] = useState("15 min");
  const [customMessage, setCustomMessage] = useState("");
  const [selectedQuickMessage, setSelectedQuickMessage] = useState(null);
  const [alertSent, setAlertSent] = useState(false);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const [keyboardVisible, setKeyboardVisible] = useState(false);

  // Predefined time options
  const timeOptions = ["15 min", "30 min", "1 hour", "2 hours", "ASAP"];

  // Enhanced quick messages with icons
  const quickMessages = [
    { icon: "car-brake-alert", text: "Please move your car in 10 minutes." },
    { icon: "car-off", text: "Your car is blocking my way. Kindly remove it." },
    { icon: "alert-circle", text: "Urgent! Your vehicle needs to be moved immediately." },
    { icon: "car-emergency", text: "Emergency situation. Please move your vehicle now." },
  ];

  // Keyboard listeners
  useEffect(() => {
    const keyboardDidShowListener = Keyboard.addListener(
      'keyboardDidShow',
      () => setKeyboardVisible(true)
    );
    const keyboardDidHideListener = Keyboard.addListener(
      'keyboardDidHide',
      () => setKeyboardVisible(false)
    );

    return () => {
      keyboardDidShowListener.remove();
      keyboardDidHideListener.remove();
    };
  }, []);

  // Animation when modal opens
  useEffect(() => {
    if (visible) {
      Animated.parallel([
        Animated.timing(slideAnim, {
          toValue: 0,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        })
      ]).start();
    } else {
      // Reset animations when modal closes
      slideAnim.setValue(50);
      fadeAnim.setValue(0);
    }
  }, [visible]);

  const sendAlert = () => {
    // Vibrate to provide feedback
    Vibration.vibrate(100);
    
    const finalMessage = selectedQuickMessage || customMessage || "Urgent: Please check your vehicle.";
    console.log(`🚨 Alert sent to User ID: ${userId} | Message: "${finalMessage}" | Time: ${selectedTime}`);

    setAlertSent(true);
    Animated.timing(fadeAnim, {
      toValue: 1,
      duration: 1000,
      useNativeDriver: true,
    }).start();

    setTimeout(() => {
      setAlertSent(false);
      fadeAnim.setValue(0);
      onClose();
      
      // Reset state for next use
      setSelectedQuickMessage(null);
      setCustomMessage("");
      setSelectedTime("15 min");
    }, 2500);
  };

  return (
    <Modal visible={visible} animationType="fade" transparent>
      <KeyboardAvoidingView 
        behavior={Platform.OS === "ios" ? "padding" : "height"}
        style={styles.container}
      >
        <SafeAreaView style={styles.modalContainer}>
          <Animated.View 
            style={[
              styles.modalContent, 
              { 
                backgroundColor: theme.cardColor,
                opacity: fadeAnim,
                transform: [{ translateY: slideAnim }]
              }
            ]}
          >
            {alertSent ? (
              <Animated.View style={[styles.successContainer, { opacity: fadeAnim }]}>
                <Icon name="check-circle" size={80} color="#4CAF50" />
                <Text style={[styles.successText, { color: theme.textColor }]}>Alert Sent!</Text>
                <Text style={[styles.successSubtext, { color: theme.textColor }]}>
                  The vehicle owner has been notified
                </Text>
              </Animated.View>
            ) : (
              <>
                <View style={styles.headerContainer}>
                  <Icon name="bell-ring-outline" size={28} color={theme.primaryColor} />
                  <Text style={[styles.modalTitle, { color: theme.textColor }]}>Send Alert</Text>
                </View>

                <ScrollView 
                  showsVerticalScrollIndicator={false}
                  contentContainerStyle={styles.scrollContent}
                >
                  {/* Urgency Level */}
                  <View style={styles.sectionContainer}>
                    <Text style={[styles.modalLabel, { color: theme.textColor }]}>
                      <Icon name="alarm" size={18} color={theme.primaryColor} /> Urgency Level
                    </Text>
                    <View style={styles.timeButtons}>
                      {timeOptions.map((time) => (
                        <TouchableOpacity
                          key={time}
                          style={[
                            styles.timeButton,
                            selectedTime === time
                              ? { backgroundColor: theme.primaryColor }
                              : { borderColor: theme.primaryColor, backgroundColor: 'rgba(0,0,0,0.05)' },
                          ]}
                          onPress={() => {
                            setSelectedTime(time);
                            Vibration.vibrate(50); // Light feedback
                          }}
                        >
                          <Text
                            style={[
                              styles.timeText,
                              { color: selectedTime === time ? theme.btnText : theme.textColor },
                            ]}
                          >
                            {time}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>

                  {/* Quick Messages */}
                  <View style={styles.sectionContainer}>
                    <Text style={[styles.modalLabel, { color: theme.textColor }]}>
                      <Icon name="message-text-outline" size={18} color={theme.primaryColor} /> Quick Messages
                    </Text>
                    <View style={styles.quickMessagesContainer}>
                      {quickMessages.map((msg, index) => (
                        <TouchableOpacity
                          key={index}
                          style={[
                            styles.quickMessage,
                            selectedQuickMessage === msg.text && { 
                              backgroundColor: theme.primaryColor,
                              borderColor: theme.primaryColor,
                            },
                            !selectedQuickMessage === msg.text && {
                              borderColor: theme.primaryColor,
                              backgroundColor: 'rgba(0,0,0,0.05)'
                            }
                          ]}
                          onPress={() => {
                            setSelectedQuickMessage(msg.text);
                            Vibration.vibrate(50); // Light feedback
                          }}
                        >
                          <Icon 
                            name={msg.icon} 
                            size={20} 
                            color={selectedQuickMessage === msg.text ? theme.btnText : theme.primaryColor} 
                            style={styles.messageIcon}
                          />
                          <Text
                            style={[
                              styles.quickMessageText,
                              { color: selectedQuickMessage === msg.text ? theme.btnText : theme.textColor },
                            ]}
                          >
                            {msg.text}
                          </Text>
                        </TouchableOpacity>
                      ))}
                    </View>
                  </View>

                  {/* Custom Message */}
                  <View style={styles.sectionContainer}>
                    <Text style={[styles.modalLabel, { color: theme.textColor }]}>
                      <Icon name="pencil-outline" size={18} color={theme.primaryColor} /> Custom Message
                    </Text>
                    <TextInput
                      style={[
                        styles.input, 
                        { 
                          color: theme.textColor, 
                          borderColor: theme.primaryColor,
                          backgroundColor: 'rgba(0,0,0,0.05)'
                        }
                      ]}
                      placeholder="Type your message..."
                      placeholderTextColor={theme.textColor + '80'}
                      value={customMessage}
                      onChangeText={setCustomMessage}
                      multiline={true}
                      numberOfLines={3}
                    />
                  </View>
                </ScrollView>

                {/* Buttons */}
                <View style={styles.modalButtons}>
                  <TouchableOpacity
                    style={[styles.modalButton, { backgroundColor: "#D32F2F" }]}
                    onPress={onClose}
                  >
                    <Icon name="close" size={20} color="#FFF" style={styles.buttonIcon} />
                    <Text style={styles.modalButtonText}>Cancel</Text>
                  </TouchableOpacity>
                  <TouchableOpacity
                    style={[styles.modalButton, { backgroundColor: theme.primaryColor }]}
                    onPress={sendAlert}
                  >
                    <Icon name="send" size={20} color="#FFF" style={styles.buttonIcon} />
                    <Text style={styles.modalButtonText}>Send Alert</Text>
                  </TouchableOpacity>
                </View>
              </>
            )}
          </Animated.View>
        </SafeAreaView>
      </KeyboardAvoidingView>
    </Modal>
  );
};

export default AlertComponent;

const styles = {
  container: {
    flex: 1,
  },
  modalContainer: {
    flex: 1,
    justifyContent: "center",
    alignItems: "center",
    backgroundColor: "rgba(0,0,0,0.6)",
    paddingHorizontal: 15,
  },
  modalContent: {
    width: "100%",
    maxHeight: height * 0.85,
    borderRadius: 20,
    padding: 20,
    elevation: 12,
    shadowColor: "#000",
    shadowOffset: { width: 0, height: 6 },
    shadowOpacity: 0.2,
    shadowRadius: 10,
  },
  scrollContent: {
    paddingBottom: 20,
  },
  headerContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    marginBottom: 20,
    paddingVertical: 10,
  },
  modalTitle: {
    fontSize: 24,
    fontWeight: "700",
    textAlign: "center",
    marginLeft: 10,
  },
  sectionContainer: {
    marginBottom: 20,
  },
  modalLabel: {
    fontSize: 16,
    marginVertical: 10,
    fontWeight: "600",
  },
  timeButtons: {
    flexDirection: "row",
    flexWrap: "wrap",
    justifyContent: "space-between",
  },
  timeButton: {
    paddingVertical: 12,
    paddingHorizontal: 10,
    borderRadius: 10,
    borderWidth: 1,
    marginVertical: 5,
    alignItems: "center",
    justifyContent: "center",
    width: '48%',
    marginBottom: 10,
  },
  timeText: {
    fontSize: 16,
    fontWeight: "600",
    textAlign: "center",
  },
  quickMessagesContainer: {
    marginVertical: 10,
  },
  quickMessage: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 12,
    paddingHorizontal: 15,
    borderRadius: 12,
    marginVertical: 8,
    borderWidth: 1,
  },
  messageIcon: {
    marginRight: 10,
  },
  quickMessageText: {
    fontSize: 15,
    fontWeight: "500",
    flex: 1,
  },
  input: {
    borderWidth: 1,
    borderRadius: 12,
    padding: 14,
    fontSize: 16,
    marginVertical: 5,
    textAlignVertical: 'top',
    minHeight: 100,
  },
  modalButtons: {
    flexDirection: "row",
    justifyContent: "space-between",
    marginTop: 15,
    paddingTop: 10,
    borderTopWidth: 1,
    borderTopColor: 'rgba(0,0,0,0.1)',
  },
  modalButton: {
    flex: 1,
    flexDirection: 'row',
    paddingVertical: 14,
    borderRadius: 12,
    alignItems: "center",
    justifyContent: 'center',
    marginHorizontal: 5,
  },
  buttonIcon: {
    marginRight: 8,
  },
  modalButtonText: {
    color: "white",
    fontSize: 16,
    fontWeight: "700",
  },
  successContainer: {
    alignItems: "center",
    justifyContent: "center",
    padding: 40,
  },
  successText: {
    fontSize: 24,
    fontWeight: "bold",
    marginTop: 20,
    textAlign: "center",
  },
  successSubtext: {
    fontSize: 16,
    marginTop: 10,
    textAlign: "center",
    opacity: 0.8,
  },
};
