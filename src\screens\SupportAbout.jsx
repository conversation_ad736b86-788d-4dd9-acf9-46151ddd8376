import React, { useState } from "react";
import { View, Text, TouchableOpacity, StyleSheet, ScrollView } from "react-native";
import Ionicons from "react-native-vector-icons/Ionicons";


const SupportAbout = () => {
  const [activeTab, setActiveTab] = useState("FAQ");
  
  const renderContent = () => {
    switch (activeTab) {
      case "FAQ":
        return (
          <ScrollView style={styles.contentContainer}>
            <Text style={styles.header}>Frequently Asked Questions</Text>
            <Text style={styles.text}>Q: How does the app work?</Text>
            <Text style={styles.answer}>A: The app helps manage parking using QR codes.</Text>
            <Text style={styles.text}>Q: How can I reset my password?</Text>
            <Text style={styles.answer}>A: Go to Profile  Change Password.</Text>
          </ScrollView>
        );
      case "TermsPrivacy":
        return (
          <ScrollView style={styles.contentContainer}>
            <Text style={styles.header}>Terms & Privacy</Text>
            <Text style={styles.text}>By using this app, you agree to our terms and privacy policy.</Text>
          </ScrollView>
        );
      case "ContactSupport":
        return (
          <View style={styles.contentContainer}>
            <Text style={styles.header}>Contact Support</Text>
            <TouchableOpacity style={styles.button} onPress={() => alert("Calling Support...")}> 
              <Ionicons name="call" size={20} color="#fff" />
              <Text style={styles.buttonText}>Call Support</Text>
            </TouchableOpacity>
            <TouchableOpacity style={styles.button} onPress={() => alert("Opening Email...")}> 
              <Ionicons name="mail" size={20} color="#fff" />
              <Text style={styles.buttonText}>Email Support</Text>
            </TouchableOpacity>
          </View>
        );
      case "AppInfo":
        return (
          <View style={styles.contentContainer}>
            <Text style={styles.header}>App Information</Text>
            <Text style={styles.text}>Version: 1.0.0</Text>
            <TouchableOpacity style={styles.button} onPress={() => alert("Checking for updates...")}> 
              <Ionicons name="refresh" size={20} color="#fff" />
              <Text style={styles.buttonText}>Check for Updates</Text>
            </TouchableOpacity>
          </View>
        );
      default:
        return null;
    }
  };
  
  return (
    <View style={styles.container}>
      <View style={styles.tabContainer}>
        {["FAQ", "TermsPrivacy", "ContactSupport", "AppInfo"].map((tab) => (
          <TouchableOpacity key={tab} onPress={() => setActiveTab(tab)} style={[styles.tab, activeTab === tab && styles.activeTab]}>
            <Text style={[styles.tabText, activeTab === tab && styles.activeTabText]}>{tab}</Text>
          </TouchableOpacity>
        ))}
      </View>
      {renderContent()}
    </View>
  );
};

const styles = StyleSheet.create({
  container: { flex: 1, backgroundColor: "#fff" },
  tabContainer: { flexDirection: "row", justifyContent: "space-around", backgroundColor: "#008080", paddingVertical: 10 },
  tab: { paddingVertical: 10, paddingHorizontal: 15 },
  activeTab: { borderBottomWidth: 2, borderBottomColor: "#fff" },
  tabText: { color: "#ddd", fontSize: 16 },
  activeTabText: { color: "#fff", fontWeight: "bold" },
  contentContainer: { padding: 20 },
  header: { fontSize: 22, fontWeight: "bold", marginBottom: 15, color: "#008080" },
  text: { fontSize: 16, color: "#333", marginBottom: 5 },
  answer: { fontSize: 14, color: "#666", marginBottom: 10 },
  button: { flexDirection: "row", backgroundColor: "#008080", padding: 14, borderRadius: 10, alignItems: "center", justifyContent: "center", marginTop: 10 },
  buttonText: { color: "#fff", fontSize: 16, fontWeight: "bold", marginLeft: 10 },
});

export default SupportAbout;
