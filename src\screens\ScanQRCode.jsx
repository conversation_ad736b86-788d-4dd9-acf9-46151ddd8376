import React, { useState, useCallback, useMemo, useRef, useEffect, useContext } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  TouchableOpacity, 
  Alert, 
  Dimensions,
  ActivityIndicator,
  StatusBar,
  Animated,
  SafeAreaView,
  Platform,
  BackHandler
} from 'react-native';
import { Camera, useCameraDevice, useCameraPermission, useCodeScanner } from 'react-native-vision-camera';
import ReactNativeHapticFeedback from 'react-native-haptic-feedback';
import { useNavigation, useFocusEffect } from '@react-navigation/native';
import { AuthContext } from '../services/AuthContext';
import Icon from "react-native-vector-icons/MaterialCommunityIcons";

const SCREEN_HEIGHT = Dimensions.get('window').height;
const SCREEN_WIDTH = Dimensions.get('window').width;
const MIN_ZOOM = 1;
const MAX_ZOOM = 5; // Increase max zoom for longer range
const DEFAULT_ZOOM = 1.5; // Start with slight zoom for better focus
const SCANNER_SIZE = SCREEN_WIDTH * 0.8;  
 
const hapticOptions = {
  enableVibrateFallback: true,
  ignoreAndroidSystemSettings: false
};

const ScanQRCode = () => {
  const navigation = useNavigation();
  const { theme } = useContext(AuthContext);
  
  const [isScanning, setIsScanning] = useState(true);
  const [hasScanned, setHasScanned] = useState(false);
  const [torch, setTorch] = useState(false);
  const [zoom, setZoom] = useState(DEFAULT_ZOOM);
  const [showZoomLevel, setShowZoomLevel] = useState(false);
  const [contactInfo, setContactInfo] = useState(null);
  const [isProcessing, setIsProcessing] = useState(false);
  const [showDetailsCard, setShowDetailsCard] = useState(false);
  const [isFocused, setIsFocused] = useState(true);
  const [isCameraInitialized, setIsCameraInitialized] = useState(false);
  const { hasPermission, requestPermission } = useCameraPermission();
  const device = useCameraDevice('back');
  const camera = useRef(null);
  
 
  const scanLineAnim = useRef(new Animated.Value(0)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;
  const welcomeOpacity = useRef(new Animated.Value(0)).current;
  
   
  const scanLineAnimation = useRef(null);
  const pulseAnimation = useRef(null);

   
  useFocusEffect(
    useCallback(() => {
      const onBackPress = () => {
        navigation.goBack();
        return true;
      };
      
      BackHandler.addEventListener('hardwareBackPress', onBackPress);
      
      return () => {
        BackHandler.removeEventListener('hardwareBackPress', onBackPress);
      };
    }, [navigation])
  );
  
 
  useEffect(() => {
    Animated.timing(welcomeOpacity, {
      toValue: 1,
      duration: 800,
      useNativeDriver: true,
    }).start();
  }, []);

   
  useEffect(() => {
    return () => {
      if (scanLineAnimation.current) {
        scanLineAnimation.current.stop();
      }
      if (pulseAnimation.current) {
        pulseAnimation.current.stop();
      }
    };
  }, []);

 
  useEffect(() => {
    if (isScanning && !hasScanned) {
       
      if (scanLineAnimation.current) {
        scanLineAnimation.current.stop();
      }
      if (pulseAnimation.current) {
        pulseAnimation.current.stop();
      }
      
      
      scanLineAnimation.current = Animated.loop(
        Animated.sequence([
          Animated.timing(scanLineAnim, {
            toValue: 1,
            duration: 2000,   
            useNativeDriver: true,
          }),
          Animated.timing(scanLineAnim, {
            toValue: 0,
            duration: 2000,
            useNativeDriver: true,
          }),
        ])
      );
      scanLineAnimation.current.start();
      
      
      pulseAnimation.current = Animated.loop(
        Animated.sequence([
          Animated.timing(pulseAnim, {
            toValue: 1.02,  
            duration: 1500,
            useNativeDriver: true,
          }),
          Animated.timing(pulseAnim, {
            toValue: 1,
            duration: 1500,
            useNativeDriver: true,
          }),
        ])
      );
      pulseAnimation.current.start();
    } else {
       
      if (scanLineAnimation.current) {
        scanLineAnimation.current.stop();
      }
      if (pulseAnimation.current) {
        pulseAnimation.current.stop();
      }
      scanLineAnim.setValue(0);
      pulseAnim.setValue(1);
    }
  }, [isScanning, hasScanned]);

   
  const handleZoomChange = useCallback((newZoom) => {
    setZoom(newZoom);
    setShowZoomLevel(true);
    if (showZoomLevelTimeout.current) {
      clearTimeout(showZoomLevelTimeout.current);
    }
    showZoomLevelTimeout.current = setTimeout(() => setShowZoomLevel(false), 1500);
  }, []);

  const showZoomLevelTimeout = useRef(null);

  const handleZoomIn = useCallback(() => {
    const newZoom = Math.min(MAX_ZOOM, zoom + 0.5);
    handleZoomChange(newZoom);
  }, [zoom, handleZoomChange]);

  const handleZoomOut = useCallback(() => {
    const newZoom = Math.max(MIN_ZOOM, zoom - 0.5);
    handleZoomChange(newZoom);
  }, [zoom, handleZoomChange]);

 
  const scanDebounceTimeout = useRef(null);
  
   
  // Optimize QR code processing for instant response
  const handleCodeScanned = useCallback((codes) => {
    if (hasScanned || isProcessing || scanDebounceTimeout.current) return;
    
    if (codes.length > 0) {
      const code = codes[0];
      
      // Ultra-short debounce for instant response
      scanDebounceTimeout.current = setTimeout(() => {
        scanDebounceTimeout.current = null;
      }, 300); // Ultra-short debounce like PhonePe
      
      try {
        // Quick validation
        if (code.type !== 'qr' || !code.value) return;

        // Provide instant feedback
        ReactNativeHapticFeedback.trigger('impactMedium', hapticOptions);
        setIsProcessing(true);
        setHasScanned(true);
        
        // Process data with minimal overhead
        try {
          let scannedData;
          try {
            scannedData = JSON.parse(code.value);
          } catch (parseError) {
            // Simplified error correction for speed
            const cleanedValue = code.value.trim();
            scannedData = JSON.parse(cleanedValue);
          }
          
          // Quick navigation without unnecessary state updates
          if (scannedData.contactNumber || scannedData.phone) {
            const phoneNumber = scannedData.contactNumber || scannedData.phone;
            const fullName = scannedData.firstName ? 
              `${scannedData.firstName} ${scannedData.lastName || ''}` : 
              scannedData.name || 'Vehicle Owner';
            
            // Navigate immediately for instant response
            navigation.navigate('CallPage', {
              ownerName: fullName,
              phoneNumber: phoneNumber,
              userId: scannedData.userId || null,
              vehicleInfo: scannedData.licensePlate ? `License: ${scannedData.licensePlate}` : null
            });
          } else {
            // Simplified error display
            setContactInfo({
              ownerName: "Error",
              vehicleInfo: "No phone number found"
            });
            setShowDetailsCard(true);
          }
          setIsProcessing(false);
        } catch (error) {
          // Simplified error handling for speed
          setIsProcessing(false);
          setHasScanned(false);
          console.log("Parse error:", error);
        }
      } catch (error) {
        setIsProcessing(false);
        setHasScanned(false);
        console.log("Scan error:", error);
      }
    }
  }, [hasScanned, isProcessing, navigation]);

 
  const codeScanner = useCodeScanner({
    codeTypes: ['qr'], // Only scan QR codes for better performance
    onCodeScanned: handleCodeScanned,
    scanMode: 'continuous',
    scannerFrameRate: 'max'
  });

  const handleScanPress = useCallback(async () => {
    if (!hasPermission) {
      const granted = await requestPermission();
      if (!granted) {
        Alert.alert(
          'Permission Required',
          'Please grant camera permission to scan QR codes',
          [{ text: 'OK' }]
        );
        return;
      }
    }
    
     
    setIsScanning(true);
    setHasScanned(false);
    setZoom(1);
     
    if (scanDebounceTimeout.current) {
      clearTimeout(scanDebounceTimeout.current);
      scanDebounceTimeout.current = null;
    }
    if (showZoomLevelTimeout.current) {
      clearTimeout(showZoomLevelTimeout.current);
      showZoomLevelTimeout.current = null;
    }
  }, [hasPermission, requestPermission]);
 
  useFocusEffect(
    useCallback(() => {
      
      setIsScanning(true);
      setHasScanned(false);
      setShowDetailsCard(false);
      setContactInfo(null);
      setIsProcessing(false);
      
      return () => {
      
        if (scanDebounceTimeout.current) {
          clearTimeout(scanDebounceTimeout.current);
          scanDebounceTimeout.current = null;
        }
        if (showZoomLevelTimeout.current) {
          clearTimeout(showZoomLevelTimeout.current);
          showZoomLevelTimeout.current = null;
        }
      };
    }, [])
  );

  // Add this to optimize performance when app is in background
  useFocusEffect(
    useCallback(() => {
      setIsFocused(true);
      return () => {
        setIsFocused(false);
      };
    }, [])
  );

  // Add this function to periodically refocus the camera
  const triggerFocus = useCallback(async () => {
    if (camera.current && isCameraInitialized && isScanning && !hasScanned) {
      try {
        // Focus on center of screen
        const centerX = SCREEN_WIDTH / 2;
        const centerY = SCREEN_HEIGHT / 2;
        
        // Request focus at the center point
        await camera.current.focus({ x: centerX, y: centerY });
        console.log("Camera focus triggered");
      } catch (error) {
        console.error("Focus error:", error);
      }
    }
  }, [isCameraInitialized, isScanning, hasScanned]);

  // Add this effect to periodically trigger focus
  useEffect(() => {
    if (!isScanning || hasScanned || !isCameraInitialized) return;
    
    // Trigger initial focus
    triggerFocus();
    
    // Set up interval to periodically refocus
    const focusInterval = setInterval(() => {
      triggerFocus();
    }, 3000); // Refocus every 3 seconds
    
    return () => clearInterval(focusInterval);
  }, [isScanning, hasScanned, isCameraInitialized, triggerFocus]);

  // Add this to optimize memory usage
  useFocusEffect(
    useCallback(() => {
      // Reset state when screen comes into focus
      setIsScanning(true);
      setHasScanned(false);
      setShowDetailsCard(false);
      setContactInfo(null);
      setIsProcessing(false);
      setZoom(DEFAULT_ZOOM); // Start with optimal zoom
      
      // Clean up animations and timers when leaving screen
      return () => {
        if (scanLineAnimation.current) {
          scanLineAnimation.current.stop();
        }
        if (pulseAnimation.current) {
          pulseAnimation.current.stop();
        }
        if (scanDebounceTimeout.current) {
          clearTimeout(scanDebounceTimeout.current);
          scanDebounceTimeout.current = null;
        }
        if (showZoomLevelTimeout.current) {
          clearTimeout(showZoomLevelTimeout.current);
          showZoomLevelTimeout.current = null;
        }
      };
    }, [])
  );

  
  const cameraComponent = useMemo(() => {
    if (!device || !isFocused) return null;
    
    return (
      <Camera
        ref={camera}
        style={StyleSheet.absoluteFill}
        device={device}
        isActive={isScanning && isFocused}
        codeScanner={codeScanner}
        torch={torch ? 'on' : 'off'}
        zoom={zoom}
        // Disable unnecessary features for better performance
        photo={false}
        video={false}
        audio={false}
        enableZoomGesture={false}
        // Minimal configuration for speed
        onInitialized={() => {
          console.log("Camera initialized");
          setIsCameraInitialized(true);
        }}
        onError={(error) => {
          console.error("Camera error:", error);
        }}
      />
    );
  }, [device, isScanning, codeScanner, torch, zoom, isFocused]);

  if (!hasPermission) {
    return (
      <SafeAreaView style={[styles.welcomeContainer, {backgroundColor: theme.backgroundColor}]}>
        <StatusBar barStyle="light-content" />
        <Animated.View 
          style={[styles.welcomeContent, { opacity: welcomeOpacity }]}
        >
          <View style={styles.iconContainer}>
            <Text style={styles.iconText}>📷</Text>
          </View>
          <Text style={[styles.welcomeTitle, {color: theme.textColor}]}>Camera Access Needed</Text>
          <Text style={[styles.welcomeText, {color: theme.textColor}]}>
            We need camera permission to scan QR codes. Your privacy is important to us.
          </Text>
          <TouchableOpacity 
            style={[styles.primaryButton, {backgroundColor: theme.primaryColor}]}
            onPress={requestPermission}
          >
            <Text style={styles.buttonText}>Grant Permission</Text>
          </TouchableOpacity>
        </Animated.View>
      </SafeAreaView>
    );
  }

  if (!device) {
    return (
      <SafeAreaView style={[styles.welcomeContainer, {backgroundColor: theme.backgroundColor}]}>
        <StatusBar barStyle="light-content" />
        <View style={styles.welcomeContent}>
          <View style={styles.iconContainer}>
            <Text style={styles.iconText}>❌</Text>
          </View>
          <Text style={[styles.welcomeTitle, {color: theme.textColor}]}>Camera Not Available</Text>
          <Text style={[styles.welcomeText, {color: theme.textColor}]}>
            We couldn't find a camera on your device. Please check your device settings.
          </Text>
        </View>
      </SafeAreaView>
    );
  }

  return (
    <View style={styles.container}>
      <StatusBar barStyle="light-content" />
      
      {/* Camera */}
      {cameraComponent}
      
      {/* Simplified Scanner Overlay */}
      <View style={styles.overlay} pointerEvents="none">
        <View style={styles.unfocusedContainer}></View>
        <View style={styles.middleContainer}>
          <View style={styles.unfocusedContainer}></View>
          <Animated.View 
            style={[
              styles.focusedContainer,
              { transform: [{ scale: pulseAnim }] }
            ]}
          >
            {/* Only render corners for better performance */}
            <View style={[styles.cornerTopLeft, styles.corner]} />
            <View style={[styles.cornerTopRight, styles.corner]} />
            <View style={[styles.cornerBottomLeft, styles.corner]} />
            <View style={[styles.cornerBottomRight, styles.corner]} />
            
            {/* Simplified scan line */}
            {isScanning && !hasScanned && (
              <Animated.View 
                style={[
                  styles.scanLine,
                  {
                    transform: [
                      {
                        translateY: scanLineAnim.interpolate({
                          inputRange: [0, 1],
                          outputRange: [0, SCANNER_SIZE],
                        }),
                      },
                    ],
                  },
                ]}
              />
            )}
          </Animated.View>
          <View style={styles.unfocusedContainer}></View>
        </View>
        <View style={styles.unfocusedContainer}></View>
      </View>
      
      {/* Minimal Status Text */}
      {!hasScanned && (
        <View style={styles.statusContainer} pointerEvents="none">
          <Text style={styles.statusText}>
            {isProcessing ? 'Processing...' : 'Align QR code'}
          </Text>
        </View>
      )}
      
      {/* Simplified Controls */}
      <SafeAreaView style={styles.controlPanelContainer}>
        <View style={styles.controlPanel}>
          <TouchableOpacity 
            style={styles.controlButton}
            onPress={() => navigation.goBack()}
          >
            <Icon name="arrow-left" size={24} color="#FFF" />
          </TouchableOpacity>
          
          <TouchableOpacity 
            style={[styles.controlButton, torch && styles.activeControlButton]}
            onPress={() => setTorch(!torch)}
          >
            <Icon name={torch ? "flashlight" : "flashlight-off"} size={24} color="#FFF" />
          </TouchableOpacity>
        </View>
      </SafeAreaView>
    </View>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: '#000',
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    zIndex: 999,  
  },
  welcomeContainer: {
    flex: 1,
  },
  welcomeContent: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 30,
  },
  iconContainer: {
    width: 100,
    height: 100,
    borderRadius: 50,
    backgroundColor: 'rgba(67, 97, 238, 0.15)',
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 30,
    borderWidth: 1,
    borderColor: 'rgba(67, 97, 238, 0.3)',
  },
  iconText: {
    fontSize: 50,
  },
  welcomeTitle: {
    fontSize: 32,
    fontWeight: 'bold',
    marginBottom: 15,
    textAlign: 'center',
  },
  welcomeText: {
    fontSize: 16,
    textAlign: 'center',
    marginBottom: 40,
    lineHeight: 24,
  },
  primaryButton: {
    paddingHorizontal: 30,
    paddingVertical: 15,
    borderRadius: 30,
    elevation: 5,
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
    width: '80%',
    alignItems: 'center',
  },
  buttonText: {
    color: 'white',
    fontSize: 18,
    fontWeight: '600',
  },
  overlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'transparent',  
  },
  unfocusedContainer: {
    flex: 1,
  },
  middleContainer: {
    flexDirection: 'row',
    flex: 1,
  },
  focusedContainer: {
    width: SCANNER_SIZE,
    height: SCANNER_SIZE,
    borderRadius: 10,  
    borderWidth: 0,  
    overflow: 'hidden',
  },
  corner: {
    position: 'absolute',
    width: 20,
    height: 20,
    borderColor: '#FFFFFF',
    borderWidth: 3,
    backgroundColor: 'transparent',
  },
  cornerTopLeft: {
    top: 0,
    left: 0,
    borderBottomWidth: 0,
    borderRightWidth: 0,
    borderTopLeftRadius: 20,
  },
  cornerTopRight: {
    top: 0,
    right: 0,
    borderBottomWidth: 0,
    borderLeftWidth: 0,
    borderTopRightRadius: 20,
  },
  cornerBottomLeft: {
    bottom: 0,
    left: 0,
    borderTopWidth: 0,
    borderRightWidth: 0,
    borderBottomLeftRadius: 20,
  },
  cornerBottomRight: {
    bottom: 0,
    right: 0,
    borderTopWidth: 0,
    borderLeftWidth: 0,
    borderBottomRightRadius: 20,
  },
  scanLine: {
    position: 'absolute',
    width: '100%',
    height: 2,
    backgroundColor: 'rgba(0, 255, 170, 0.8)',
    shadowColor: '#00FFAA',
    shadowOffset: { width: 0, height: 0 },
    shadowOpacity: 0.9,
    shadowRadius: 10,
    elevation: 5,
  },
  controlPanelContainer: {
    position: 'absolute',
    bottom: 0,
    left: 0,
    right: 0,
  },
  controlPanel: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 15,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  controlButton: {
    padding: 10,
    borderRadius: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    alignItems: 'center',
    justifyContent: 'center',
  },
  activeControlButton: {
    backgroundColor: 'rgba(255, 255, 255, 0.4)',
  },
  controlButtonText: {
    color: '#FFF',
    marginLeft: 5,
  },
  zoomControls: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  zoomButton: {
    padding: 10,
    marginHorizontal: 5,
    backgroundColor: 'rgba(255, 255, 255, 0.2)',
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  zoomButtonText: {
    color: '#FFF',
    fontSize: 18,
  },
  zoomLevelIndicator: {
    marginHorizontal: 10,
  },
  zoomLevelText: {
    color: '#FFF',
    fontSize: 18,
  },
  statusContainer: {
    position: 'absolute',
    top: 20,
    left: 20,
    right: 20,
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
    padding: 15,
    borderRadius: 10,
  },
  statusText: {
    color: '#FFF',
    fontSize: 18,
    marginBottom: 5,
  },
  statusHint: {
    color: '#FFF',
    fontSize: 16,
  },
  loader: {
    marginTop: 5,
  },
  zoomLevelPopup: {
    position: 'absolute',
    top: 50,
    left: 50,
    right: 50,
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    padding: 10,
    borderRadius: 10,
    justifyContent: 'center',
    alignItems: 'center',
  },
  zoomLevelPopupText: {
    color: '#FFF',
    fontSize: 18,
  },
  detailsCardContainer: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
  },
  detailsCard: {
    width: '80%',
    backgroundColor: '#FFF',
    borderRadius: 10,
    padding: 20,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.3,
    shadowRadius: 5,
  },
  detailsHeader: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 15,
  },
  detailsTitle: {
    fontSize: 20,
    fontWeight: 'bold',
  },
  closeButton: {
    padding: 10,
    borderRadius: 5,
    backgroundColor: '#4361ee',
  },
  closeButtonText: {
    color: '#FFF',
    fontSize: 18,
  },
  detailsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    marginBottom: 10,
  },
  detailsLabel: {
    fontSize: 16,
    fontWeight: 'bold',
  },
  detailsValue: {
    fontSize: 16,
  },
  scanAgainButton: {
    backgroundColor: '#4361ee',
    padding: 10,
    borderRadius: 5,
    alignItems: 'center',
    justifyContent: 'center',
  },
  scanAgainButtonText: {
    color: '#FFF',
    fontSize: 18,
  },
  cameraView: {
    width: Dimensions.get('window').width,
    height: Dimensions.get('window').height,
  },
  guideContainer: {
    position: 'absolute',
    bottom: 120,
    alignSelf: 'center',
    alignItems: 'center',
  },
  guideText: {
    color: '#FFFFFF',
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 10,
    textShadowColor: 'rgba(0, 0, 0, 0.75)',
    textShadowOffset: { width: 0, height: 1 },
    textShadowRadius: 2,
  },
  guideDots: {
    flexDirection: 'row',
    justifyContent: 'center',
    width: 60,
  },
  guideDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
    backgroundColor: 'rgba(255, 255, 255, 0.5)',
    marginHorizontal: 4,
  },
  activeGuideDot: {
    backgroundColor: '#FFFFFF',
    transform: [{ scale: 1.2 }],
  },
  statusMessage: {
    position: 'absolute',
    top: SCREEN_HEIGHT * 0.15,
    alignSelf: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.7)',
    paddingHorizontal: 15,
    paddingVertical: 8,
    borderRadius: 20,
    maxWidth: '80%',
  },
  statusMessageText: {
    color: '#FFFFFF',
    fontSize: 14,
    fontWeight: '500',
  },
});

export default ScanQRCode;
